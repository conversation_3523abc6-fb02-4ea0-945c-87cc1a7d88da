import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { createSelector } from "reselect";
import { RootState } from "@/store";

interface DiscourseState {
  loading: boolean;
  error: string | null;
  ssoUrl: string | null;
  communityName: string | null;
  discourseUrl: string | null;
  ssoEnabled: boolean;
}

// Initial state
const initialState: DiscourseState = {
  loading: false,
  error: null,
  ssoUrl: null,
  communityName:
    process.env.NEXT_PUBLIC_DISCOURSE_COMMUNITY_NAME || "Community",
  discourseUrl:
    process.env.NEXT_PUBLIC_DISCOURSE_URL || "https://affitor.discourse.group",
  ssoEnabled: true,
};

const discourseSlice = createSlice({
  name: "discourse",
  initialState,
  reducers: {
    // Get SSO URL
    getDiscourseSSOUrl: {
      reducer: (state) => {
        state.loading = true;
        state.error = null;
      },
      prepare: (payload?: { sso?: string; sig?: string }) => ({
        payload: payload || {},
      }),
    },
    setDiscourseSSOUrl: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.ssoUrl = action.payload;
    },

    // Get Discourse config
    getDiscourseConfig: (state) => {
      state.loading = true;
      state.error = null;
    },
    setDiscourseConfig: (
      state,
      action: PayloadAction<{
        discourseUrl: string;
        communityName: string;
        ssoEnabled: boolean;
      }>
    ) => {
      state.loading = false;
      state.discourseUrl = action.payload.discourseUrl;
      state.communityName = action.payload.communityName;
      state.ssoEnabled = action.payload.ssoEnabled;
    },

    // Utility actions
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
});

export const { actions, reducer } = discourseSlice;

// Selectors
const selectDiscourseState = (state: RootState) => state.discourse;

export const selectDiscourseLoading = createSelector(
  [selectDiscourseState],
  (discourseState) => discourseState.loading
);

export const selectDiscourseError = createSelector(
  [selectDiscourseState],
  (discourseState) => discourseState.error
);

export const selectDiscourseSSOUrl = createSelector(
  [selectDiscourseState],
  (discourseState) => discourseState.ssoUrl
);

export const selectDiscourseConfig = createSelector(
  [selectDiscourseState],
  (discourseState) => ({
    discourseUrl: discourseState.discourseUrl,
    communityName: discourseState.communityName,
    ssoEnabled: discourseState.ssoEnabled,
  })
);
