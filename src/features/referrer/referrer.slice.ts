import { RootState } from "@/store";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface ReferrerState {
  loading: boolean;
  error: string | null;
  success: boolean;
}

const initialState: ReferrerState = {
  loading: false,
  error: null,
  success: false,
};

const referrerSlice = createSlice({
  name: "referrer",
  initialState,
  reducers: {
    registerReferrer: (state) => {
      state.loading = true;
      state.error = null;
      state.success = false;
    },
    registerReferrerSuccess: (state) => {
      state.loading = false;
      state.error = null;
      state.success = true;
    },
    registerReferrerFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
      state.success = false;
    },
    resetReferrerState: (state) => {
      state.loading = false;
      state.error = null;
      state.success = false;
    },
  },
});

// Export actions
export const { actions } = referrerSlice;

// Export selectors
export const selectReferrerLoading = (state: RootState) => state.referrer.loading;
export const selectReferrerError = (state: RootState) => state.referrer.error;
export const selectReferrerSuccess = (state: RootState) => state.referrer.success;

// Export reducer
export default referrerSlice.reducer;
