import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { createSelector } from "reselect";
import { RootState } from "@/store";
import { ITransaction } from "@/interfaces";

export interface TransactionPagination {
  page: number;
  pageSize: number;
  pageCount: number;
  total: number;
}

interface TransactionState {
  transactions: ITransaction[];
  loading: boolean;
  error: string | null;
  pagination: TransactionPagination | null;
}

const initialState: TransactionState = {
  transactions: [],
  loading: false,
  error: null,
  pagination: null,
};

const transactionSlice = createSlice({
  name: "transaction",
  initialState,
  reducers: {
    // Fetch transactions
    fetchTransactionsRequest: (
      state,
      action: PayloadAction<{
        page?: number;
        pageSize?: number;
        search?: string;
        sort?: string;
      }>
    ) => {
      state.loading = true;
      state.error = null;
    },

    fetchTransactionsSuccess: (
      state,
      action: PayloadAction<{
        data: ITransaction[];
        meta: TransactionPagination;
      }>
    ) => {
      state.loading = false;
      state.transactions = action.payload.data;
      state.pagination = action.payload.meta;
      state.error = null;
    },

    fetchTransactionsFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Clear transactions
    clearTransactions: (state) => {
      state.transactions = [];
      state.pagination = null;
      state.error = null;
    },

    // Set loading state
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
  },
});

export const actions = transactionSlice.actions;
export const reducer = transactionSlice.reducer;

// Base selector
const selectTransactionState = (state: RootState) => state.transaction;

// Memoized selectors using createSelector
export const selectTransactions = createSelector(
  [selectTransactionState],
  (transactionState) => transactionState.transactions
);

export const selectTransactionsLoading = createSelector(
  [selectTransactionState],
  (transactionState) => transactionState.loading
);

export const selectTransactionsError = createSelector(
  [selectTransactionState],
  (transactionState) => transactionState.error
);

export const selectTransactionsPagination = createSelector(
  [selectTransactionState],
  (transactionState) => transactionState.pagination
);
