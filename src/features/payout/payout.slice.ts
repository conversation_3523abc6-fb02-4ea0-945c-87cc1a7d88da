import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface PayoutOverview {
  total_earned: number;
  pending: number;
  paid: number;
}

interface PayoutHistoryItem {
  id: number;
  documentId: string;
  payout_status: "pending" | "paid" | "failed";
  method: string;
  amount: number;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  processing_fee: number;
  payout_date: string | null;
}

interface AdminPayoutItem {
  id: number;
  documentId: string;
  partner: {
    id: number;
    user: {
      id: number;
      username: string;
      email: string;
      first_name?: string;
      last_name?: string;
    };
  };
  payment_method: string;
  commission_cycle: string;
  amount: number;
  payout_status: "pending" | "approved" | "completed";
  createdAt: string;
  updatedAt: string;
  payout_date?: string | null;
  days_pending?: number;
  auto_approved?: boolean;
}

interface AdminPayoutResponse {
  data: AdminPayoutItem[];
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

interface PayoutHistoryResponse {
  data: PayoutHistoryItem[];
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

interface PayoutState {
  overview: PayoutOverview | null;
  history: PayoutHistoryItem[];
  historyMeta: PayoutHistoryResponse["meta"] | null;
  loading: boolean;
  historyLoading: boolean;
  error: string | null;
  historyError: string | null;

  // Admin payout management
  adminPayouts: AdminPayoutItem[];
  adminPayoutsMeta: AdminPayoutResponse["meta"] | null;
  adminPayoutsLoading: boolean;
  adminPayoutsError: string | null;
  adminPayoutsSuccess: string | null;
  selectedPayouts: string[];

  // Separate error state for payout creation (modal-specific)
  createPayoutError: string | null;
}

const initialState: PayoutState = {
  overview: null,
  history: [],
  historyMeta: null,
  loading: false,
  historyLoading: false,
  error: null,
  historyError: null,

  // Admin payout management
  adminPayouts: [],
  adminPayoutsMeta: null,
  adminPayoutsLoading: false,
  adminPayoutsError: null,
  adminPayoutsSuccess: null,
  selectedPayouts: [],

  // Separate error state for payout creation (modal-specific)
  createPayoutError: null,
};

const payoutSlice = createSlice({
  name: "payout",
  initialState,
  reducers: {
    // Fetch payout overview
    fetchPayoutOverview: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchPayoutOverviewSuccess: (
      state,
      action: PayloadAction<PayoutOverview>
    ) => {
      state.loading = false;
      state.overview = action.payload;
      state.error = null;
    },
    fetchPayoutOverviewFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Fetch payout history
    fetchPayoutHistory: (
      state,
      action: PayloadAction<{ page?: number; pageSize?: number }>
    ) => {
      state.historyLoading = true;
      state.historyError = null;
    },
    fetchPayoutHistorySuccess: (
      state,
      action: PayloadAction<PayoutHistoryResponse>
    ) => {
      state.historyLoading = false;
      state.history = action.payload.data;
      state.historyMeta = action.payload.meta;
      state.historyError = null;
    },
    fetchPayoutHistoryFailure: (state, action: PayloadAction<string>) => {
      state.historyLoading = false;
      state.historyError = action.payload;
    },

    // Clear payout state
    clearPayoutState: (state) => {
      state.overview = null;
      state.history = [];
      state.historyMeta = null;
      state.loading = false;
      state.historyLoading = false;
      state.error = null;
      state.historyError = null;
      state.adminPayouts = [];
      state.adminPayoutsMeta = null;
      state.adminPayoutsLoading = false;
      state.adminPayoutsError = null;
      state.selectedPayouts = [];
      state.createPayoutError = null;
    },

    // Admin payout management actions
    fetchAdminPayoutsRequest: (
      state,
      action: PayloadAction<{
        payout_status?: "pending" | "approved" | "completed";
        page?: number;
        pageSize?: number;
        search?: string;
      }>
    ) => {
      state.adminPayoutsLoading = true;
      state.adminPayoutsError = null;
    },
    fetchAdminPayoutsSuccess: (
      state,
      action: PayloadAction<AdminPayoutResponse>
    ) => {
      console.log("action", action.payload);
      state.adminPayoutsLoading = false;
      state.adminPayouts = action.payload.data;
      console.log("state.adminPayouts", state.adminPayouts);
      state.adminPayoutsMeta = action.payload.meta;
      state.adminPayoutsError = null;
    },
    fetchAdminPayoutsFailure: (state, action: PayloadAction<string>) => {
      state.adminPayoutsLoading = false;
      state.adminPayoutsError = action.payload;
    },

    // Approve payout
    approvePayoutRequest: (state, action: PayloadAction<string>) => {
      state.adminPayoutsLoading = true;
      state.adminPayoutsError = null;
    },
    approvePayoutSuccess: (state, action: PayloadAction<AdminPayoutItem>) => {
      state.adminPayoutsLoading = false;
      const index = state.adminPayouts.findIndex(
        (payout) => payout.documentId === action.payload.documentId
      );
      if (index !== -1) {
        state.adminPayouts[index] = action.payload;
      }
      state.adminPayoutsError = null;
      state.adminPayoutsSuccess = "Payout approved successfully";
    },
    approvePayoutFailure: (state, action: PayloadAction<string>) => {
      state.adminPayoutsLoading = false;
      state.adminPayoutsError = action.payload;
    },

    // Mark as paid
    markAsPaidRequest: (state, action: PayloadAction<string | string[]>) => {
      state.adminPayoutsLoading = true;
      state.adminPayoutsError = null;
    },
    markAsPaidSuccess: (state, action: PayloadAction<AdminPayoutItem[]>) => {
      state.adminPayoutsLoading = false;
      const updatedPayouts = Array.isArray(action.payload)
        ? action.payload
        : [];
      updatedPayouts.forEach((updatedPayout) => {
        const index = state.adminPayouts.findIndex(
          (payout) => payout.documentId === updatedPayout.documentId
        );
        if (index !== -1) {
          state.adminPayouts[index] = updatedPayout;
        }
      });
      state.selectedPayouts = [];
      state.adminPayoutsError = null;
      state.adminPayoutsSuccess = `${updatedPayouts.length} payout${
        updatedPayouts.length > 1 ? "s" : ""
      } marked as paid successfully`;
    },
    markAsPaidFailure: (state, action: PayloadAction<string>) => {
      state.adminPayoutsLoading = false;
      state.adminPayoutsError = action.payload;
    },

    // Selection management
    togglePayoutSelection: (state, action: PayloadAction<string>) => {
      const payoutId = action.payload;
      const index = state.selectedPayouts.indexOf(payoutId);
      if (index === -1) {
        state.selectedPayouts.push(payoutId);
      } else {
        state.selectedPayouts.splice(index, 1);
      }
    },
    selectAllPayouts: (state, action: PayloadAction<string[]>) => {
      state.selectedPayouts = action.payload;
    },
    clearPayoutSelection: (state) => {
      state.selectedPayouts = [];
    },

    // Archive payout
    archivePayoutRequest: (state, action: PayloadAction<string>) => {
      state.adminPayoutsLoading = true;
      state.adminPayoutsError = null;
    },
    archivePayoutSuccess: (state, action: PayloadAction<AdminPayoutItem>) => {
      state.adminPayoutsLoading = false;
      const index = state.adminPayouts.findIndex(
        (payout) => payout.documentId === action.payload.documentId
      );
      if (index !== -1) {
        state.adminPayouts.splice(index, 1); // Remove archived payout from list
      }
      state.adminPayoutsError = null;
      state.adminPayoutsSuccess = "Payout archived successfully";
    },
    archivePayoutFailure: (state, action: PayloadAction<string>) => {
      state.adminPayoutsLoading = false;
      state.adminPayoutsError = action.payload;
    },

    // Export payouts
    exportPayoutsRequest: (
      state,
      action: PayloadAction<{
        payout_status?: "pending" | "approved" | "completed";
        format?: "csv" | "excel";
        search?: string;
      }>
    ) => {
      state.adminPayoutsLoading = true;
      state.adminPayoutsError = null;
    },
    exportPayoutsSuccess: (state) => {
      state.adminPayoutsLoading = false;
      state.adminPayoutsError = null;
      state.adminPayoutsSuccess = "Payouts exported successfully";
    },
    exportPayoutsFailure: (state, action: PayloadAction<string>) => {
      state.adminPayoutsLoading = false;
      state.adminPayoutsError = action.payload;
    },

    // Create payout
    createPayoutRequest: (
      state,
      action: PayloadAction<{
        partnerId: string;
        amount: number;
        method: "paypal" | "bank transfer";
        payout_status: "pending" | "approved" | "completed";
      }>
    ) => {
      state.adminPayoutsLoading = true;
      state.createPayoutError = null;
    },
    createPayoutSuccess: (state) => {
      state.adminPayoutsLoading = false;
      // Don't add to list directly - let the refresh handle it
      state.createPayoutError = null;
      state.adminPayoutsSuccess = "Payout created successfully";
    },
    createPayoutFailure: (state, action: PayloadAction<string>) => {
      state.adminPayoutsLoading = false;
      state.createPayoutError = action.payload;
    },

    // Clear success message
    clearPayoutSuccess: (state) => {
      state.adminPayoutsSuccess = null;
    },

    // Clear create payout error
    clearCreatePayoutError: (state) => {
      state.createPayoutError = null;
    },
  },
});

export const { actions, reducer } = payoutSlice;

// Selectors
export const selectPayoutOverview = (state: any) => state.payout.overview;
export const selectPayoutLoading = (state: any) => state.payout.loading;
export const selectPayoutError = (state: any) => state.payout.error;
export const selectPayoutHistory = (state: any) => state.payout.history;
export const selectPayoutHistoryMeta = (state: any) => state.payout.historyMeta;
export const selectPayoutHistoryLoading = (state: any) =>
  state.payout.historyLoading;
export const selectPayoutHistoryError = (state: any) =>
  state.payout.historyError;

// Admin payout selectors
export const selectAdminPayouts = (state: any) => state.payout.adminPayouts;
export const selectAdminPayoutsMeta = (state: any) =>
  state.payout.adminPayoutsMeta;
export const selectAdminPayoutsLoading = (state: any) =>
  state.payout.adminPayoutsLoading;
export const selectAdminPayoutsError = (state: any) =>
  state.payout.adminPayoutsError;
export const selectSelectedPayouts = (state: any) =>
  state.payout.selectedPayouts;
export const selectAdminPayoutsSuccess = (state: any) =>
  state.payout.adminPayoutsSuccess;
export const selectCreatePayoutError = (state: any) =>
  state.payout.createPayoutError;
