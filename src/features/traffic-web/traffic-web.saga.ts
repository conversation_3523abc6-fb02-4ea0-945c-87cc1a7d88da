import { call, put, takeEvery } from "redux-saga/effects";
import { actions } from "./traffic-web.slice";
import { PayloadAction } from "@reduxjs/toolkit";
import qs from "qs";
import { IPagination, ISort } from "@/interfaces";
import { handleApiError } from "@/utils/error-handler";

function* handleFetch(
  action: PayloadAction<{
    affiliateDocId: string;
    sort?: ISort;
    pagination?: IPagination;
  }>
): Generator<any, void, any> {
  try {
    yield put(actions.setLoading(true));
    const { affiliateDocId, pagination, sort } = action.payload;

    const filters = {
      affiliate: {
        documentId: affiliateDocId,
      },
    };

    const query = qs.stringify(
      {
        filters,
        ...(pagination && {
          pagination: {
            page: pagination.page,
            pageSize: pagination.pageSize || 10,
          },
        }),
        ...(sort && {
          sort: [sort].flat().map((s) => `${s.field}:${s.order}`),
        }),
      },
      {
        encodeValuesOnly: true,
      }
    );

    const response: any = yield call(fetch, `/api/traffic-web?${query}`);
    if (!response.ok) {
      // Use the centralized error handler
      if (yield call(handleApiError, response)) {
        return; // Error was handled
      }
      
      yield put(
        actions.setError(`Request failed with status ${response.status}`)
      );
      return;
    }

    const { data, meta } = yield response.json();

    if (!data || !meta) {
      yield put(actions.setError("Invalid response structure"));
      return;
    }

    yield put(actions.setTrafficWebs(data));
    yield put(actions.setPagination(meta.pagination));
  } catch (error: any) {
    yield put(actions.setError("Error fetching traffic web data"));
  } finally {
    yield put(actions.setLoading(false));
  }
}

export default function* trafficWebSaga() {
  yield takeEvery(actions.fetch.type, handleFetch);
}
