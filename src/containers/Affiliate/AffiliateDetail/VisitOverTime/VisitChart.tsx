import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from "chart.js";
import { useTheme } from "next-themes";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

// Custom plugin for the shadow
const shadowPlugin = {
  id: 'shadowPlugin',
  beforeDraw: (chart: ChartJS) => {
    const { ctx } = chart;
    ctx.save();
    const originalLineDraw = chart.legend?.fit;
    if (originalLineDraw) {
        // We are going to hijack the draw function of the chart
        // and draw a shadow before the original line is drawn
        // chart.draw = function () {
            // originalLineDraw.apply(this, arguments);
            const { ctx } = chart;
            ctx.save();
            ctx.shadowColor = 'rgba(59, 130, 246, 0.5)';
            ctx.shadowBlur = 10;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 4;
            // originalLineDraw.apply(this, arguments);
            ctx.restore();
        // };
    }
  }
};


export default function VisitChart({
  labels = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul"],
  data = [65, 59, 80, 81, 56, 55, 40],
}: {
  labels?: string[];
  data?: number[];
}) {
  const { theme } = useTheme();
  const isDarkMode = theme === "dark";

  const gridColor = isDarkMode ? "rgba(255, 255, 255, 0.1)" : "rgba(0, 0, 0, 0.1)";
  const ticksColor = isDarkMode ? "rgba(255, 255, 255, 0.7)" : "rgba(0, 0, 0, 0.7)";
  const backgroundColor = isDarkMode ? "rgba(59, 130, 246, 0.1)" : "rgba(59, 130, 246, 0.2)";
  const borderColor = isDarkMode ? "rgba(59, 130, 246, 0.8)" : "rgba(59, 130, 246, 1)";
  
  return (
    <div style={{ width: "100%", height: "100%" }}>
      <Line
        data={{
          labels: labels,
          datasets: [
            {
              label: "Visits",
              data: data,
              fill: true,
              backgroundColor: backgroundColor,
              borderColor: borderColor,
              tension: 0.4,
              pointBackgroundColor: borderColor,
              pointBorderColor: "#fff",
              pointHoverBackgroundColor: "#fff",
              pointHoverBorderColor: borderColor,
            },
          ],
        }}
        options={{
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false,
            },
            tooltip: {
              backgroundColor: isDarkMode ? '#000' : '#fff',
              titleColor: isDarkMode ? '#fff' : '#000',
              bodyColor: isDarkMode ? '#fff' : '#000',
              borderColor: gridColor,
              borderWidth: 1,
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              grid: {
                color: gridColor,
              },
              ticks: {
                color: ticksColor,
                callback: function (valueStr: string | number) {
                  const value = parseInt(valueStr.toString());
                  if (value >= 1000000) {
                    return (value / 1000000).toFixed(1) + "M";
                  } else if (value >= 1000) {
                    return (value / 1000).toFixed(1) + "K";
                  }
                  return value;
                },
              },
            },
            x: {
              grid: {
                color: gridColor,
              },
              ticks: {
                color: ticksColor,
              }
            }
          },
        }}
        plugins={[shadowPlugin]}
      />
    </div>
  );
}
