"use client";
import React, { useEffect, useMemo } from "react";
import ListAffiliate from "./ListAffiliate";
import FilterTagsBar from "./FilterTagsBar";
import FilterCategories from "./FilterCategories";
import AffiliateFilterBar from "@/components/AffiliateFilterBar";
import { FilterState } from "@/interfaces";
import { useDispatch, useSelector } from "react-redux";
import { affiliateActions, categoryActions } from "@/features/rootActions"; // Fixed import
import { actions as filterActions } from "@/features/filter/filter.slice";
import {
  selectFilters,
  selectActiveTag,
  selectIsHashInitialized,
  selectActiveFilterCount,
} from "@/features/filter/filter.slice";

interface AffiliatesProps {
  industryPath?: string;
  categoryPath?: string;
  hideFilterCategories?: boolean;
  shortLinkChecked?: boolean; // Add flag to know if short link check is complete
}

// Helper function to parse URL hash to filters (excluding category)
const hashToFilters = (hash: string): FilterState | null => {
  if (!hash || hash === "#") return null;

  // Initialize with empty filter structure
  const result: FilterState = {
    pricing: { from: "", to: "" },
    commission: { from: "", to: "" },
    conversion: { from: "", to: "" },
    monthlyTraffic: { from: "", to: "" },
    cookiesDuration: { from: "", to: "" },
    category: "", // Category will be handled by URL path, not hash
    paymentMethod: "",
    recurring: "",
    countries: [],
    launchYears: []
  };

  // Remove the leading # and split by #
  const hashParts = hash.substring(1).split("#");
  console.log("Hash parts:", hashParts);

  for (const part of hashParts) {
    // First split by the first = sign only
    const equalSignIndex = part.indexOf("=");
    if (equalSignIndex === -1) continue;

    const key = part.substring(0, equalSignIndex);
    const value = part.substring(equalSignIndex + 1);

    console.log(`Processing hash part - key: ${key}, value: ${value}`);

    if (!key || !value) continue;

    // Skip category processing in hash - it's now handled by URL path
    if (key === "category" || key === "c") {
      continue; // Skip category from hash processing
    } else if (key === "tag" || key === "t") {
      // We store tag ID separately in Redux but not in the FilterState
      // We'll handle this outside the returned FilterState
    } else if (key === "payment_method" || key === "pm") {
      result.paymentMethod = decodeURIComponent(value);
    } else if (key === "recurring" || key === "r") {
      result.recurring = decodeURIComponent(value);
    } else if (
      key === "pricing" ||
      key === "p" ||
      key === "commission" ||
      key === "com" ||
      key === "conversion" ||
      key === "conv" ||
      key === "monthly_traffic" ||
      key === "mt" ||
      key === "cookies_duration" ||
      key === "cd"
    ) {
      // For range values, we need to handle the format key=from=value,to=value
      const rangeObject: { from: string; to: string } = {
        from: "",
        to: "",
      };

      // Fix bug: Correctly parse comma-separated range values
      if (value.includes(",")) {
        // Split by comma to get individual range parts
        const rangeParts = value.split(",");

        // Process each part (from=X, to=Y)
        for (const rangePart of rangeParts) {
          // For each range part like "from=20" or "to=50"
          const rangeEqualIndex = rangePart.indexOf("=");
          if (rangeEqualIndex === -1) continue;

          const rangeKey = rangePart.substring(0, rangeEqualIndex);
          const rangeValue = rangePart.substring(rangeEqualIndex + 1);

          console.log(`  Range part - ${rangeKey}: ${rangeValue}`);

          // Assign to the correct property in rangeObject
          if (rangeKey === "from" || rangeKey === "f") {
            rangeObject.from = rangeValue;
          } else if (rangeKey === "to" || rangeKey === "t") {
            rangeObject.to = rangeValue;
          }
        }
      }

      // Map keys to the correct filter property
      let filterKey: keyof FilterState | null = null;
      if (key === "pricing" || key === "p") filterKey = "pricing";
      else if (key === "commission" || key === "com") filterKey = "commission";
      else if (key === "conversion" || key === "conv") filterKey = "conversion";
      else if (key === "monthly_traffic" || key === "mt")
        filterKey = "monthlyTraffic";
      else if (key === "cookies_duration" || key === "cd")
        filterKey = "cookiesDuration";
      else if (key === "c") {
        // Handle countries array
        result.countries = value.split(",").map(v => decodeURIComponent(v));
        console.log(`  Set countries to:`, result.countries);
        continue;
      }
      else if (key === "ly") {
        // Handle launch years array
        result.launchYears = value.split(",").map(v => decodeURIComponent(v));
        console.log(`  Set launchYears to:`, result.launchYears);
        continue;
      }

      if (filterKey) {
        // Set the range values
        result[filterKey] = {
          from: rangeObject.from || "",
          to: rangeObject.to || "",
        };
        console.log(`  Set ${filterKey} to:`, result[filterKey]);
      }
    }
  }

  console.log("Final parsed filters:", result);
  return result;
};

// Helper function to convert filters to hash (excluding category)
const filtersToHash = (filters: FilterState, tagId?: string): string => {
  const hashParts: string[] = [];

  // Skip category in hash generation - it's now handled by URL path
  // Category will not be included in hash anymore

  if (tagId) {
    hashParts.push(`t=${encodeURIComponent(tagId)}`);
  }

  if (filters.paymentMethod) {
    hashParts.push(`pm=${encodeURIComponent(filters.paymentMethod)}`);
  }

  if (filters.recurring) {
    hashParts.push(`r=${encodeURIComponent(filters.recurring)}`);
  }

  // Add range filters with shortened keys
  const addRangeFilter = (
    key: string,
    shortKey: string,
    filter: { from: string; to: string }
  ) => {
    if (filter.from || filter.to) {
      const parts: string[] = [];
      if (filter.from) parts.push(`f=${filter.from}`);
      if (filter.to) parts.push(`t=${filter.to}`);

      hashParts.push(`${shortKey}=${parts.join(",")}`);
    }
  };

  // Use shortened keys for all filters
  addRangeFilter("pricing", "p", filters.pricing);
  addRangeFilter("commission", "com", filters.commission);
  addRangeFilter("conversion", "conv", filters.conversion);
  addRangeFilter("monthlyTraffic", "mt", filters.monthlyTraffic);
  addRangeFilter("cookiesDuration", "cd", filters.cookiesDuration);

  // Add country and launch year filters
  if (filters.countries && filters.countries.length > 0) {
    hashParts.push(`c=${encodeURIComponent(filters.countries.join(","))}`);
  }

  if (filters.launchYears && filters.launchYears.length > 0) {
    hashParts.push(`ly=${encodeURIComponent(filters.launchYears.join(","))}`);
  }

  // Reconstruct hash
  return hashParts.length > 0 ? "#" + hashParts.join("#") : "";
};

// Helper function to update URL hash without refresh
const updateUrlHash = (hash: string) => {
  if (typeof window === "undefined") return;

  // Use replaceState to update URL without navigation
  window.history.replaceState(
    null, // Don't need state data
    "", // No title needed
    hash || window.location.pathname + window.location.search
  );
};

const Affiliates: React.FC<AffiliatesProps> = ({
  industryPath,
  categoryPath,
  hideFilterCategories,
  shortLinkChecked = true, // Default to true for backward compatibility
}) => {
  const dispatch = useDispatch();
  const { setAffiliates } = affiliateActions;

  // Get filter state from Redux
  const filters = useSelector(selectFilters);
  const activeTag = useSelector(selectActiveTag);
  const isHashInitialized = useSelector(selectIsHashInitialized);
  const activeFilterCount = useSelector(selectActiveFilterCount);

  // Get categories to check if categoryPath matches any category
  const categories = useSelector((state: any) => state.category.list);
  const categoriesLoading = useSelector((state: any) => state.category.loading);

  // Memoize the matching category to prevent unnecessary re-calculations
  const matchingCategory = useMemo(() => {
    if (categories.length > 1 && categoryPath) {
      return categories.find((cat: any) => cat.slug === categoryPath);
    }
    return null;
  }, [categories.length, categoryPath]);

  // Check if categoryPath matches a category and set it as active (only from URL)
  useEffect(() => {
    // Only process if short link check is complete and this is from URL navigation
    if (
      shortLinkChecked &&
      matchingCategory &&
      filters.category !== categoryPath
    ) {
      console.log(
        `Affiliate/index: URL category "${categoryPath}" matches category:`,
        matchingCategory.name
      );

      // Set the category as active in both filter and category stores
      dispatch(filterActions.setActiveCategory(matchingCategory));
      dispatch(categoryActions.setCurrentCategory(matchingCategory));

      // Also update the filter state to include this category
      dispatch(
        filterActions.updateFilter({
          key: "category",
          value: categoryPath,
        })
      );
    } else if (shortLinkChecked && categoryPath && !matchingCategory) {
      console.log(
        `Affiliate/index: Category path "${categoryPath}" does not match any category`
      );
    } else if (
      shortLinkChecked &&
      categories.length > 1 &&
      (!categoryPath || categoryPath === "") &&
      filters.category !== ""
    ) {
      // No categoryPath means we're on homepage - set "All Programs" as active
      const allProgramsCategory = categories.find(
        (cat: any) => cat.name === "All Programs" || cat.slug === ""
      );

      if (allProgramsCategory) {
        console.log(
          "Affiliate/index: Homepage detected - setting 'All Programs' category as active"
        );
        dispatch(filterActions.setActiveCategory(allProgramsCategory));
        dispatch(categoryActions.setCurrentCategory(allProgramsCategory));

        // Clear any category filter since we're showing all programs
        dispatch(
          filterActions.updateFilter({
            key: "category",
            value: "",
          })
        );
      }
    }
  }, [
    shortLinkChecked,
    matchingCategory,
    categoryPath,
    categories.length,
    filters.category,
    dispatch,
    setAffiliates, // Add this to ensure data refresh when category changes
  ]);

  // Load categories if not loaded yet
  useEffect(() => {
    if (categories.length <= 1) {
      console.log("Loading categories for category path matching");
      dispatch(categoryActions.fetchAll());
    }
  }, [dispatch, categories.length]);

  // Process URL hash on initial load
  useEffect(() => {
    if (typeof window !== "undefined" && !isHashInitialized) {
      console.log("Initializing from URL hash");

      // Get hash and handle trailing slashes in URL
      let hash = window.location.hash;
      let tagId = "";

      if (!hash && window.location.pathname.endsWith("/")) {
        // If URL ends with slash, check if there's a hash in the URL without the slash
        const urlWithoutSlash = window.location.href.slice(0, -1);
        const hashIndex = urlWithoutSlash.indexOf("#");
        if (hashIndex !== -1) {
          hash = urlWithoutSlash.slice(hashIndex);
        }
      }

      if (hash) {
        // Extract tag ID separately, it needs special handling
        // Support both old and new format
        const tagMatch = hash.match(/#(tag|t)=([^#]*)/);
        if (tagMatch && tagMatch[2]) {
          tagId = decodeURIComponent(tagMatch[2]);
        }

        const parsedFilters = hashToFilters(hash);
        console.log("🔍 Parsed filters from URL hash:", parsedFilters);

        if (parsedFilters) {
          // Set filters in global state
          dispatch(filterActions.setFilters(parsedFilters));

          // If category is set in hash, update the category in the store
          if (parsedFilters.category) {
            // Also update the category in the filter state
            dispatch(
              filterActions.updateFilter({
                key: "category",
                value: parsedFilters.category,
              })
            );
          }

          // If tag ID is set in hash, load it
          if (tagId) {
            // We'll handle this in FilterTagsBar with the initialTagId
          }
        }
      }

      // Mark hash as initialized to prevent multiple initializations
      dispatch(filterActions.setHashInitialized(true));

      // Signal that filters changed to trigger data loading
      dispatch(setAffiliates(null));
    }
  }, [dispatch, setAffiliates, isHashInitialized]);

  // Update URL hash when filters change
  useEffect(() => {
    if (isHashInitialized) {
      const newHash = filtersToHash(filters, activeTag?.id);
      updateUrlHash(newHash);
    }
  }, [filters, activeTag, isHashInitialized]);

  return (
    <div className="min-h-screen max-w-[1400px] mx-auto py-4">
      <main className="container mt-[20px] mx-auto px-4 max-w-[1400px]">
        <section className="mb-4">
          <h2 className="text-[28px] font-bold mb-4 text-center w-full">
            Top Affiliate Programs Curated by Us
          </h2>
        </section>
        <AffiliateFilterBar />
        <div>
          <FilterTagsBar initialTagId={activeTag?.id} />
        </div>
        <div className="overflow-x-auto border-solid border-[1px] bg-background">
          {!hideFilterCategories && <FilterCategories />}
          <ListAffiliate />
        </div>
      </main>
    </div>
  );
};

export default Affiliates;
