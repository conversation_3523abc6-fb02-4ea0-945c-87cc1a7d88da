"use client";
import { useState, useEffect, useRef, useCallback, useMemo } from "react";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useDispatch, useSelector } from "react-redux";
import { RotateCcw, X } from "lucide-react";
import {
  selectCategories,
  selectPaymentMethods,
  selectPaymentMethodsLoading,
} from "@/features/selectors";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  selectFilters,
  selectActiveFilterCount,
  actions as filterActions,
} from "@/features/filter/filter.slice";
import { affiliateActions } from "@/features/rootActions";

// Add useMediaQuery hook for responsive behavior
const useMediaQuery = (query: string): boolean => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const media = window.matchMedia(query);
    if (media.matches !== matches) {
      setMatches(media.matches);
    }

    const listener = () => setMatches(media.matches);
    media.addEventListener("change", listener);

    return () => media.removeEventListener("change", listener);
  }, [matches, query]);

  return matches;
};

// Custom hook for stable inputs
const useStableInput = (
  initialValue: string,
  onChange: (value: string) => void
) => {
  const [value, setValue] = useState(initialValue);

  // Update when initialValue changes from props
  useEffect(() => {
    setValue(initialValue);
  }, [initialValue]);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setValue(newValue);
  }, []);

  const handleBlur = useCallback(() => {
    onChange(value);
  }, [value, onChange]);

  return {
    value,
    onChange: handleChange,
    onBlur: handleBlur,
  };
};

// Input component that uses the stable input hook
const StableInput = ({
  value,
  onChange,
  placeholder = "Min",
  unit,
  className = "",
  ...props
}: {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  unit?: string;
  className?: string;
  [key: string]: any;
}) => {
  const input = useStableInput(value, onChange);

  return (
    <div className="flex-1 relative">
      <input
        type="text"
        inputMode="decimal"
        className={`w-full border border-input bg-background rounded-md px-3 py-1 ${
          unit ? "pr-12" : "pr-3"
        } h-8 text-foreground shadow-none outline-none focus:outline-none focus:border-blue-500 focus:ring-0 text-[16px] md:text-sm ${className}`}
        placeholder={placeholder}
        value={input.value}
        onChange={input.onChange}
        onBlur={input.onBlur}
        style={{
          fontSize: "16px", // Prevent zoom on mobile
          transformOrigin: "left center",
        }}
        {...props}
      />
      {unit && (
        <span className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground text-xs">
          {unit}
        </span>
      )}
    </div>
  );
};

export default function FilterButton() {
  const dispatch = useDispatch();

  // Get data from Redux selectors
  const categories = useSelector(selectCategories) || [];
  const paymentMethodsFromApi = useSelector(selectPaymentMethods);
  const isLoadingPaymentMethods = useSelector(selectPaymentMethodsLoading);
  const activeFilterCount = useSelector(selectActiveFilterCount);

  // Get the filters from Redux state
  const filters = useSelector(selectFilters);

  // Initialize with default values for all filter fields
  const defaultFilters = useMemo(
    () => ({
      pricing: { from: "", to: "" },
      commission: { from: "", to: "" },
      conversion: { from: "", to: "" },
      monthlyTraffic: { from: "", to: "" },
      cookiesDuration: { from: "", to: "" },
      category: "",
      paymentMethod: "",
      recurring: "",
      countries: [],
      launchYears: [],
    }),
    []
  );

  // Local state for managing the form
  const [localFilters, setLocalFilters] = useState(filters);

  // UI states
  const [isOpen, setIsOpen] = useState(false);
  const [wasReset, setWasReset] = useState(false);
  const [categoryOpen, setCategoryOpen] = useState(false);
  const [paymentMethodOpen, setPaymentMethodOpen] = useState(false);

  // Store the original filters in a ref to avoid dependency changes in effects
  const originalFiltersRef = useRef(filters);

  // Update local filters when Redux filters change
  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  // Update ref when we open the filter popup
  useEffect(() => {
    if (isOpen) {
      // When popover opens, store the current filters as original
      originalFiltersRef.current = { ...filters };
      setWasReset(false);
    } else if (wasReset) {
      // When popover closes and filters were reset but not applied, revert to original
      setLocalFilters({ ...originalFiltersRef.current });
      setWasReset(false);
    }
  }, [isOpen, wasReset, filters]);

  // Format number with commas for display - memoize pure functions
  const formatNumberWithCommas = useCallback((value: string): string => {
    if (!value) return "";
    return Number(value).toLocaleString("en-US");
  }, []);

  // Remove commas for storing in state - memoize pure functions
  const unformatNumber = useCallback((value: string): string => {
    if (!value) return "";
    return value.replace(/,/g, "");
  }, []);

  // Use callback for better memoization of event handlers
  const handleInputChange = useCallback(
    (
      category:
        | "pricing"
        | "commission"
        | "conversion"
        | "monthlyTraffic"
        | "cookiesDuration",
      field: "from" | "to",
      value: string
    ) => {
      // Filter to allow only numbers and decimal point
      const numericValue = value.replace(/[^0-9.]/g, "");

      // For monthly traffic, handle the formatting
      if (category === "monthlyTraffic") {
        const unformattedValue = unformatNumber(numericValue);
        setLocalFilters((prev) => {
          // Avoid unnecessary updates
          if (prev[category][field] === unformattedValue) return prev;

          return {
            ...prev,
            [category]: {
              ...prev[category],
              [field]: unformattedValue,
            },
          };
        });
      } else {
        setLocalFilters((prev) => {
          // Avoid unnecessary updates
          if (prev[category][field] === numericValue) return prev;

          return {
            ...prev,
            [category]: {
              ...prev[category],
              [field]: numericValue,
            },
          };
        });
      }
    },
    [unformatNumber]
  );

  // Memoize handlers
  const handlePaymentMethodSelect = useCallback((value: string) => {
    setLocalFilters((prev) => {
      if (prev.paymentMethod === value) return prev;
      return {
        ...prev,
        paymentMethod: value,
      };
    });
    setPaymentMethodOpen(false);
  }, []);

  const handleApplyFilter = useCallback(() => {
    // Check if filters have actually changed by comparing with original filters
    const isFilterChanged =
      JSON.stringify(localFilters) !==
      JSON.stringify(originalFiltersRef.current);

    if (isFilterChanged) {
      // Only update the global state if filters have changed
      dispatch(filterActions.setFilters(localFilters));

      // Signal loading state for affiliate data
      dispatch(affiliateActions.setAffiliates(null));
    }

    // Close the popup in all cases
    setIsOpen(false);
  }, [dispatch, localFilters]);

  const handleResetFilters = useCallback(() => {
    setLocalFilters(defaultFilters);
    setWasReset(true); // Mark that filters were reset
  }, [defaultFilters]);

  // Define a handler for close events to check if we need to revert
  const handleOpenChange = useCallback(
    (open: boolean) => {
      if (!open && wasReset) {
        // If closing without applying after a reset, revert to original
        setLocalFilters(originalFiltersRef.current);
        setWasReset(false);
      }
      setIsOpen(open);
    },
    [wasReset]
  );

  // Add handlers for clearing individual filter types
  const handleClearPricingFilter = useCallback(() => {
    setLocalFilters((prev) => ({
      ...prev,
      pricing: { from: "", to: "" },
    }));
  }, []);

  const handleClearCommissionFilter = useCallback(() => {
    setLocalFilters((prev) => ({
      ...prev,
      commission: { from: "", to: "" },
    }));
  }, []);

  const handleClearConversionFilter = useCallback(() => {
    setLocalFilters((prev) => ({
      ...prev,
      conversion: { from: "", to: "" },
    }));
  }, []);

  const handleClearTrafficFilter = useCallback(() => {
    setLocalFilters((prev) => ({
      ...prev,
      monthlyTraffic: { from: "", to: "" },
    }));
  }, []);

  const handleClearCookiesDurationFilter = useCallback(() => {
    setLocalFilters((prev) => ({
      ...prev,
      cookiesDuration: { from: "", to: "" },
    }));
  }, []);

  const handleClearPaymentMethodFilter = useCallback(() => {
    setLocalFilters((prev) => ({
      ...prev,
      paymentMethod: "",
    }));
  }, []);

  // Add handler for clearing recurring filter
  const handleClearRecurringFilter = useCallback(() => {
    setLocalFilters((prev) => ({
      ...prev,
      recurring: "",
    }));
  }, []);

  const handleRecurringSelect = useCallback((value: string) => {
    setLocalFilters((prev) => {
      if (prev.recurring === value) return prev;
      return {
        ...prev,
        recurring: value,
      };
    });
  }, []);

  // Memoize computed values
  const paymentMethodOptions = useMemo(() => {
    if (paymentMethodsFromApi && paymentMethodsFromApi.length > 0) {
      return paymentMethodsFromApi.map((method) => ({
        value: method.documentId,
        label: method.name,
      }));
    }
    return [];
  }, [paymentMethodsFromApi]);

  // Memoize the trigger button to prevent re-renders
  const triggerButton = useMemo(
    () => (
      <button className="flex items-center gap-2 px-3 py-2 rounded-lg bg-secondary hover:bg-primary hover:text-primary-foreground transition-color shadow-sm text-[14px] text-secondary-foreground">
        <div className="relative">
          <i className="fas fa-filter"></i>
          {activeFilterCount > 0 && (
            <span className="absolute -top-2 -right-2 flex items-center justify-center w-4 h-4 bg-primary text-primary-foreground text-[10px] font-bold rounded-full">
              {activeFilterCount}
            </span>
          )}
        </div>
      </button>
    ),
    [activeFilterCount]
  );

  // Memoize the FilterContent component to prevent unnecessary re-renders
  const FilterContent = useCallback(() => {
    // Check if filters have actually changed by comparing with original filters
    const isFilterChanged =
      JSON.stringify(localFilters) !==
      JSON.stringify(originalFiltersRef.current);

    return (
      <div className="grid grid-cols-1 gap-3">
        {/* Individual filter items with consistent single-column layout */}
        {/* Each filter takes up a full row for clarity */}

        {/* Category Section - Priority 1 */}
        {/* <div className="mb-1">
          <div className="flex flex-row gap-2 items-center">
            <label className="text-sm font-medium text-foreground w-1/3">
              Category
            </label>
            <div className="relative w-2/3">
              <Select
                value={localFilters.category}
                onValueChange={handleCategorySelect}
              >
                <SelectTrigger className="w-full h-8">
                  <SelectValue placeholder="Select categories..." />
                </SelectTrigger>
                <SelectContent className="max-h-[200px]">
                  {categories
                    .filter((category) => category.id)
                    .map((category) => (
                      <SelectItem
                        key={category.id || `cat-${category.name}`}
                        value={category.id || ""}
                      >
                        {category.name}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
              {localFilters.category && (
                <button
                  onClick={handleClearCategoryFilter}
                  className="absolute right-8 top-1/2 -translate-y-1/2 flex items-center justify-center h-5 w-5 rounded-full bg-gray-200 hover:bg-gray-300 transition-colors"
                >
                  <X className="h-3 w-3" />
                </button>
              )}
            </div>
          </div>
        </div> */}

        {/* Commission Section - Priority 2 */}
        <div className="mb-1">
          <div className="flex flex-row gap-2 items-center">
            <label className="text-sm font-medium text-foreground w-1/3">
              Commission (%)
            </label>
            <div className="flex gap-2 w-2/3">
              <StableInput
                value={localFilters.commission.from}
                onChange={(value) =>
                  handleInputChange("commission", "from", value)
                }
                unit="%"
                placeholder="Min"
              />
              <StableInput
                value={localFilters.commission.to}
                onChange={(value) =>
                  handleInputChange("commission", "to", value)
                }
                unit="%"
                placeholder="Max"
              />
            </div>
          </div>
        </div>

        {/* EPU Section - Priority 3 */}
        <div className="mb-1">
          <div className="flex flex-row gap-2 items-center">
            <label className="text-sm font-medium text-foreground w-1/3">
              EPU
            </label>
            <div className="flex gap-2 w-2/3">
              <StableInput
                value={localFilters.conversion.from}
                onChange={(value) =>
                  handleInputChange("conversion", "from", value)
                }
                unit="$"
                placeholder="Min"
              />
              <StableInput
                value={localFilters.conversion.to}
                onChange={(value) =>
                  handleInputChange("conversion", "to", value)
                }
                unit="$"
                placeholder="Max"
              />
            </div>
          </div>
        </div>

        {/* Pricing Section - Priority 4 */}
        <div className="mb-1">
          <div className="flex flex-row gap-2 items-center">
            <label className="text-sm font-medium text-foreground w-1/3">
              Pricing ($)
            </label>
            <div className="flex gap-2 w-2/3">
              <StableInput
                value={localFilters.pricing.from}
                onChange={(value) =>
                  handleInputChange("pricing", "from", value)
                }
                unit="$"
                placeholder="Min"
              />
              <StableInput
                value={localFilters.pricing.to}
                onChange={(value) => handleInputChange("pricing", "to", value)}
                unit="$"
                placeholder="Max"
              />
            </div>
          </div>
        </div>

        {/* Recurring Section - Priority 5 */}
        <div className="mb-1">
          <div className="flex flex-row gap-2 items-center">
            <label className="text-sm font-medium text-foreground w-1/3">
              Recurring
            </label>
            <div className="relative w-2/3">
              <Select
                value={localFilters.recurring || ""}
                onValueChange={handleRecurringSelect}
              >
                <SelectTrigger className="w-full h-8">
                  <SelectValue placeholder="Select recurring type..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="one_time">One time</SelectItem>
                  <SelectItem value="life_time">Life time</SelectItem>
                  <SelectItem value="less_than_12">
                    Shorter than 12 months
                  </SelectItem>
                  <SelectItem value="more_than_or_equal_12">
                    12 months or longer
                  </SelectItem>
                </SelectContent>
              </Select>
              {localFilters.recurring && (
                <button
                  onClick={handleClearRecurringFilter}
                  className="absolute right-8 top-1/2 -translate-y-1/2 flex items-center justify-center h-5 w-5 rounded-full bg-gray-200 hover:bg-gray-300 transition-colors"
                >
                  <X className="h-3 w-3" />
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Monthly Traffic Section - Priority 6 */}
        <div className="mb-1">
          <div className="flex flex-row gap-2 items-center">
            <label className="text-sm font-medium text-foreground w-1/3">
              Monthly Traffic
            </label>
            <div className="flex gap-2 w-2/3">
              <StableInput
                value={
                  localFilters.monthlyTraffic.from
                    ? formatNumberWithCommas(localFilters.monthlyTraffic.from)
                    : ""
                }
                onChange={(value) =>
                  handleInputChange("monthlyTraffic", "from", value)
                }
                placeholder="Min"
              />
              <StableInput
                value={
                  localFilters.monthlyTraffic.to
                    ? formatNumberWithCommas(localFilters.monthlyTraffic.to)
                    : ""
                }
                onChange={(value) =>
                  handleInputChange("monthlyTraffic", "to", value)
                }
                placeholder="Max"
              />
            </div>
          </div>
        </div>

        {/* Cookies Duration Section - Priority 7 */}
        <div className="mb-1">
          <div className="flex flex-row gap-2 items-center">
            <label className="text-sm font-medium text-foreground w-1/3">
              Cookies (days)
            </label>
            <div className="flex gap-2 w-2/3">
              <StableInput
                value={localFilters.cookiesDuration.from}
                onChange={(value) =>
                  handleInputChange("cookiesDuration", "from", value)
                }
                unit="days"
                placeholder="Min"
              />
              <StableInput
                value={localFilters.cookiesDuration.to}
                onChange={(value) =>
                  handleInputChange("cookiesDuration", "to", value)
                }
                unit="days"
                placeholder="Max"
              />
            </div>
          </div>
        </div>

        {/* Payment Methods Section - Priority 8 */}
        <div className="mb-1">
          <div className="flex flex-row gap-2 items-center">
            <label className="text-sm font-medium text-foreground w-1/3">
              Payment Methods
            </label>
            <div className="relative w-2/3">
              <Select
                value={localFilters.paymentMethod}
                onValueChange={handlePaymentMethodSelect}
              >
                <SelectTrigger className="w-full h-8">
                  <SelectValue
                    placeholder={
                      isLoadingPaymentMethods
                        ? "Loading..."
                        : "Select methods..."
                    }
                  />
                </SelectTrigger>
                <SelectContent className="max-h-[200px]">
                  {isLoadingPaymentMethods ? (
                    <SelectItem value="loading" disabled>
                      Loading payment methods...
                    </SelectItem>
                  ) : (
                    paymentMethodOptions.map((method) => (
                      <SelectItem key={method.value} value={method.value}>
                        {method.label}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
              {localFilters.paymentMethod && (
                <button
                  onClick={handleClearPaymentMethodFilter}
                  className="absolute right-8 top-1/2 -translate-y-1/2 flex items-center justify-center h-5 w-5 rounded-full bg-gray-200 hover:bg-gray-300 transition-colors"
                >
                  <X className="h-3 w-3" />
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between gap-4 mt-4">
          <button
            className="text-blue-800 py-1 px-3 h-8 rounded-md hover:text-blue-900 transition flex items-center justify-center gap-1 bg-transparent cursor-pointer text-sm"
            onClick={handleResetFilters}
          >
            <RotateCcw className="h-3 w-3" />
            <span>Reset</span>
          </button>
          <button
            className={`py-1 px-4 h-8 rounded-md transition flex items-center justify-center text-sm cursor-${
              isFilterChanged ? "pointer" : "not-allowed"
            } ${
              isFilterChanged
                ? "bg-blue-600 text-white hover:bg-blue-900"
                : "bg-gray-400 text-gray-100"
            }`}
            onClick={handleApplyFilter}
            disabled={!isFilterChanged}
          >
            Apply Filters
          </button>
        </div>
      </div>
    );
  }, [
    localFilters,
    categories,
    isLoadingPaymentMethods,
    paymentMethodOptions,
    handleInputChange,
    formatNumberWithCommas,
    handleResetFilters,
    handleApplyFilter,
    handleClearPaymentMethodFilter,
    handleClearRecurringFilter,
    handleRecurringSelect,
    originalFiltersRef, // Added originalFiltersRef to dependencies
  ]);

  // Add media query to detect mobile screens
  const isMobile = useMediaQuery("(max-width: 768px)");

  // Conditional rendering based on screen size
  if (isMobile) {
    return (
      <Dialog open={isOpen} onOpenChange={handleOpenChange}>
        <DialogTrigger asChild>{triggerButton}</DialogTrigger>
        <DialogContent className="w-[90vw] p-4 max-h-[80vh]">
          <DialogHeader className="px-0">
            <DialogTitle className="text-lg font-bold">
              Filter Affiliate Programs
            </DialogTitle>
          </DialogHeader>
          <div className="overflow-y-auto px-1 pb-4">
            <FilterContent />
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Popover open={isOpen} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>{triggerButton}</PopoverTrigger>
      <PopoverContent
        className="w-[90vw] md:w-[min(700px,60vw)] p-4 bg-popover border-border max-h-[80vh] rounded-lg shadow-md"
        align="end"
        alignOffset={-5}
        sideOffset={5}
      >
        <h3 className="font-bold text-lg mb-3 text-left text-foreground">
          Filter Affiliate Programs
        </h3>
        <FilterContent />
      </PopoverContent>
    </Popover>
  );
}
