import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import SlideShow from "@/components/SlideShow";
import { actions } from "@/features/auth/auth.slice";

// Fixed utility function to detect in-app browsers - correctly handles Safari on mobile
const isInAppBrowser = (): boolean => {
  const ua =
    navigator.userAgent || navigator.vendor || (window as any).opera || "";

  // Common in-app browser checks (unchanged)
  if (
    ua.indexOf("FBAN") !== -1 ||
    ua.indexOf("FBAV") !== -1 ||
    ua.indexOf("Messenger") !== -1
  ) {
    return true;
  }

  // Instagram
  if (ua.indexOf("Instagram") !== -1) {
    return true;
  }

  // Twitter/X
  if (ua.indexOf("Twitter") !== -1 || ua.indexOf("X App") !== -1) {
    return true;
  }

  // LINE
  if (ua.indexOf("Line") !== -1) {
    return true;
  }

  // Telegram
  if (ua.indexOf("Telegram") !== -1 || ua.indexOf("TelegramBot") !== -1) {
    return true;
  }

  // Zalo
  if (ua.indexOf("ZaloApp") !== -1 || ua.indexOf("Zalo") !== -1) {
    return true;
  }

  // WeChat
  if (ua.indexOf("MicroMessenger") !== -1) {
    return true;
  }

  // LinkedIn
  if (ua.indexOf("LinkedIn") !== -1) {
    return true;
  }

  // Pinterest
  if (ua.indexOf("Pinterest") !== -1) {
    return true;
  }

  // TikTok
  if (ua.indexOf("TikTok") !== -1) {
    return true;
  }

  // Snapchat
  if (ua.indexOf("Snapchat") !== -1) {
    return true;
  }

  // Reddit
  if (ua.indexOf("Reddit") !== -1) {
    return true;
  }

  // Check for regular Safari (mobile or desktop) - these should NOT be treated as in-app browsers
  const isRegularSafari =
    ua.indexOf("Safari") !== -1 &&
    ua.indexOf("Version/") !== -1 &&
    ua.indexOf("Chrome") === -1 &&
    ua.indexOf("Android") === -1 &&
    // Make sure third-party app indicators are not present
    ua.indexOf("Mobile/") === -1;

  // If it's definitely Safari, return false (not an in-app browser)
  if (isRegularSafari) {
    console.log("Detected as regular Safari, not an in-app browser");
    return false;
  }

  // Modified Android WebView detection - be more specific
  const isAndroidWebview =
    ua.indexOf("wv") !== -1 ||
    (ua.indexOf("Android") !== -1 &&
      ua.indexOf("Version/") !== -1 &&
      ua.indexOf("Chrome/") !== -1);

  // Modified iOS WKWebView detection to avoid catching regular Safari
  const isIOSWKWebView =
    (/iPhone|iPad|iPod/.test(ua) && !ua.includes("Safari")) ||
    (ua.includes("iPhone") &&
      ua.includes("AppleWebKit") &&
      (!ua.includes("Safari") || ua.includes("GSA/")) &&
      !ua.includes("CriOS") &&
      !ua.includes("FxiOS"));

  // Fixed iOS SFSafariViewController detection to prevent false positives with Safari
  // Most reliable way: if there's an app-specific string alongside Safari AND AppleWebKit
  const hasAppIndicators =
    ua.includes("Instagram") ||
    ua.includes("FBIOS") ||
    ua.includes("Twitter") ||
    ua.includes("Snapchat");

  const isIOSSFSafariViewController =
    /iPhone|iPad|iPod/.test(ua) &&
    ua.includes("Safari") &&
    ua.includes("AppleWebKit") &&
    !ua.includes("CriOS") &&
    !ua.includes("FxiOS") &&
    hasAppIndicators;

  // Add debug logging to help troubleshoot
  console.log("Browser detection:", {
    userAgent: ua,
    isAndroidWebview,
    isIOSWKWebView,
    isIOSSFSafariViewController,
  });

  return isAndroidWebview || isIOSWKWebView || isIOSSFSafariViewController;
};

// AuthToggle Component
interface AuthToggleProps {
  isSignUp: boolean;
  toggleAuthMode: () => void;
}

const AuthToggle: React.FC<AuthToggleProps> = ({
  isSignUp,
  toggleAuthMode,
}) => {
  return (
    <div className="text-center mb-6">
      <p className="text-sm text-muted-foreground">
        {/* {isSignUp ? "Already have an account?" : "Don't have an account?"}
        <button
          type="button"
          className="ml-1 text-blue-500 hover:underline focus:outline-none cursor-pointer"
          onClick={toggleAuthMode}
        >
          {isSignUp ? "Sign In" : "Sign Up"}
        </button> */}
      </p>
    </div>
  );
};

// SocialLoginButtons Component
interface SocialLoginMethod {
  name: string;
  color: string;
  icon: any;
  onClick?: () => void;
  loading?: boolean;
  disabled?: boolean;
}

interface SocialLoginButtonsProps {
  loginMethods: SocialLoginMethod[];
  isSignUp: boolean;
}

const SocialLoginButtons: React.FC<SocialLoginButtonsProps> = ({
  loginMethods,
  isSignUp,
}) => {
  return (
    <div className="space-y-3">
      {loginMethods.map((method, index) => (
        <button
          key={index}
          className={`flex items-center justify-center w-full p-3 ${method.color} rounded-lg cursor-pointer hover:shadow-md transition-shadow duration-200`}
          onClick={method.onClick}
          disabled={method.disabled}
        >
          {method.loading ? (
            <span className="mr-2">
              <svg
                className="animate-spin h-4 w-4 text-[#4285F4]"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
            </span>
          ) : (
            <span className="mr-2">{method.icon}</span>
          )}
          {isSignUp
            ? `Sign up with ${method.name}`
            : `Sign in with ${method.name}`}
        </button>
      ))}
    </div>
  );
};

// Main SignInContainer Component
const SignInContainer: React.FC = () => {
  const dispatch = useDispatch();
  const authState = useSelector((state: any) => state.auth);
  const {
    loading,
    error: authError,
    authSuccess,
    socialLoginLoading,
  } = authState;

  const [isSignUp, setIsSignUp] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [showInAppBrowserModal, setShowInAppBrowserModal] = useState(false);

  // New function to force opening in external browser - simplified version
  const openInExternalBrowser = () => {
    console.log("Opening in default browser");

    // Check platform to use the most direct method
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    const isAndroid = /Android/.test(navigator.userAgent);

    if (isIOS) {
      // For iOS, this scheme will typically open Safari
      window.location.href = `x-safari-${window.location.href}`;
    } else if (isAndroid) {
      // For Android, use an intent URL that opens the default browser
      window.location.href = `intent:${window.location.href}#Intent;scheme=https;action=android.intent.action.VIEW;end`;
    } else {
      // For other platforms, try the standard approach
      window.location.href = window.location.href;
    }
  };

  // Display auth error from Redux in the UI
  useEffect(() => {
    if (authError) {
      // You can handle the error display here or use a toast notification
      console.error("Authentication error:", authError);
    }
  }, [authError]);

  // Debug log for loading state
  useEffect(() => {
    console.log("Auth state updated:", {
      loading,
      socialLoginLoading,
      authError,
      authSuccess,
      isAuthenticated: authState.isAuthenticated,
    });
  }, [loading, socialLoginLoading, authError, authSuccess, authState]);

  // Clear any previous errors when component mounts
  useEffect(() => {
    dispatch(actions.clearMessages());
  }, [dispatch]);

  // Check if the screen is mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => {
      window.removeEventListener("resize", checkMobile);
    };
  }, []);

  const loginMethods = [
    {
      name: "Google",
      color: "bg-background border-border border text-foreground shadow-sm",
      icon: (
        <svg aria-hidden="true" className="w-4 h-4" viewBox="0 0 18 18">
          <path
            fill="#4285F4"
            d="M16.51 8H8.98v3h4.3c-.18 1-.74 1.48-1.6 2.04v2.01h2.6a7.8 7.8 0 0 0 2.38-5.88c0-.57-.05-.66-.15-1.18"
          ></path>
          <path
            fill="#34A853"
            d="M8.98 17c2.16 0 3.97-.72 5.3-1.94l-2.6-2a4.8 4.8 0 0 1-7.18-2.54H1.83v2.07A8 8 0 0 0 8.98 17"
          ></path>
          <path
            fill="#FBBC05"
            d="M4.5 10.52a4.8 4.8 0 0 1 0-3.04V5.41H1.83a8 8 0 0 0 0 7.18z"
          ></path>
          <path
            fill="#EA4335"
            d="M8.98 4.18c1.17 0 2.23.4 3.06 1.2l2.3-2.3A8 8 0 0 0 1.83 5.4L4.5 7.49a4.8 4.8 0 0 1 4.48-3.3"
          ></path>
        </svg>
      ),
      onClick: () => {
        // Clear any previous errors before starting new auth
        dispatch(actions.clearMessages());

        console.log("Google sign-in button clicked");
        console.log("Current user agent:", navigator.userAgent);
        console.log("Current loading states:", { loading, socialLoginLoading });

        // Check if already in progress
        if (socialLoginLoading || loading) {
          console.log("Authentication already in progress, ignoring click");
          return;
        }

        // Check if using in-app browser before proceeding
        if (isInAppBrowser()) {
          console.log("In-app browser detected, forcing external browser");
          openInExternalBrowser();
          return; // Don't proceed with sign in
        }

        // Double-check for Safari to ensure we're not incorrectly blocking it
        const ua = navigator.userAgent.toLowerCase();
        const isMobileSafari =
          /iphone|ipod|ipad/.test(ua) &&
          ua.includes("safari") &&
          !ua.includes("chrome") &&
          !ua.includes("android");

        if (isMobileSafari) {
          console.log("Confirmed regular Safari, proceeding with sign-in");
        }

        console.log("Dispatching Firebase Google sign-in action");
        // Dispatch Firebase Google sign in action
        dispatch(actions.firebaseGoogleSignIn());
      },
      loading: socialLoginLoading,
      disabled: socialLoginLoading || loading,
    },
  ];

  return (
    <div className="h-[calc(100vh-75px)] flex justify-center items-center font-sans overflow-auto px-2">
      {/* Sign-in form container with fixed width for better responsiveness */}
      <div className="w-full max-w-md px-6 py-8 bg-background flex flex-col items-center justify-center border shadow-lg rounded-lg px-2">
        <div className="w-full">
          <h2 className="text-xl md:text-4xl font-semibold text-start mb-2 text-foreground">
            {isSignUp ? "Create an Account" : "Sign In"}
          </h2>
          <p className="text-muted-foreground mb-6 text-sm">
            {isSignUp
              ? "Join our affiliate marketing platform using your Google account"
              : "Welcome! Please sign in with your Google account"}
          </p>

          {/* {renderErrorMessage()} */}
          {/* {renderSuccessMessage()} */}

          {!authSuccess ? (
            <>
              {/* Email/password form has been removed in favor of Google sign-in only */}
              {/* Direct users to use the Google sign-in option */}
              {/* <div className="text-center mb-6">
                <p className="text-muted-foreground">
                  Please use Google authentication to {isSignUp ? "create an account" : "sign in"}
                </p>
              </div> */}

              {/* Show loading state debug info */}
              {loading && (
                <div className="text-center text-xs text-primary mt-2">
                  Loading state is active
                </div>
              )}

              {/* Google sign-in button is now the primary authentication method */}
              <SocialLoginButtons
                loginMethods={loginMethods}
                isSignUp={isSignUp}
              />

              {/* Keep the AuthToggle to switch between sign-in and sign-up messaging */}
              <AuthToggle
                isSignUp={isSignUp}
                toggleAuthMode={() => setIsSignUp(!isSignUp)}
              />
            </>
          ) : (
            // Show verification instructions if registration was successful
            <div className="text-center my-8">
              <h3 className="text-lg font-semibold mb-2 text-foreground">
                Check Your Email
              </h3>
              <p className="text-muted-foreground mb-4">
                Please check your inbox for instructions to verify your account.
              </p>
              <button
                onClick={() => dispatch(actions.setAuthSuccess(null))}
                className="text-primary hover:underline"
              >
                Return to Sign In
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Modal is no longer needed as we're directly opening external browser */}
    </div>
  );
};

export default SignInContainer;
