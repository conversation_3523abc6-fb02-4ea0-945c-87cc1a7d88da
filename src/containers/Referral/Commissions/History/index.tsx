import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectReferralCommissions,
  selectReferralCommissionsLoading,
  selectReferralCommissionsPagination,
} from "@/features/selectors";
import { referralCommissionActions } from "@/features/rootActions";
import { ReferralCommission } from "@/features/referral-commission/referral-commission.slice";
import TableCommissions, {
  CommissionItem,
} from "@/components/TableCommissions";

interface HistoryProps {
  loading?: boolean;
}

const History: React.FC<HistoryProps> = ({
  loading: externalLoading = false,
}) => {
  const dispatch = useDispatch();

  // Redux selectors
  const commissions = useSelector(selectReferralCommissions);
  const isLoading = useSelector(selectReferralCommissionsLoading);
  const pagination = useSelector(selectReferralCommissionsPagination);

  const [currentPage, setCurrentPage] = useState(1);

  // Transform backend data to match TableCommissions interface
  const transformCommissionData = (
    commissions: ReferralCommission[]
  ): CommissionItem[] => {
    return commissions.map((commission) => ({
      id: commission.id.toString(),
      date: new Date(commission.createdAt).toLocaleDateString("en-US", {
        month: "numeric",
        day: "numeric",
        year: "numeric",
      }),
      user: {
        username: commission.referral.user?.username || "Unknown",
        email: commission.referral.user?.email || "Unknown",
      },
      product: commission.subscription_tier?.display_name || "Unknown",
      productType: commission.subscription_tier?.display_name || "Unknown",
      grossSale: commission.gross_sale_amount,
      commission: commission.commission_amount,
      status: (() => {
        // Map commission status to table status
        if (commission.commission_status === "pending") return "Under Review";
        if (commission.commission_status === "paid") return "Paid";
        if (commission.commission_status === "ready_to_pay")
          return "Ready To Pay";
        return commission.commission_status as
          | "Paid"
          | "Ready To Pay"
          | "Under Review";
      })(),
    }));
  };

  // Only fetch data when page changes (not on mount)
  useEffect(() => {
    // Only fetch if we're not on the first page or if we don't have data yet
    if (currentPage > 1) {
      dispatch(
        referralCommissionActions.fetchCommissionsRequest({
          page: currentPage,
          pageSize: 25,
        })
      );
    }
  }, [dispatch, currentPage]);

  const goToPage = (page: number) => {
    setCurrentPage(page);
  };

  // Transform commission data for table
  const commissionHistory = transformCommissionData(commissions);

  // Use the loading state from Redux or external prop
  const tableLoading = isLoading || externalLoading;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Commission History
        </h3>
      </div>

      <TableCommissions
        data={commissionHistory}
        isLoading={tableLoading}
        enablePagination={true}
        pagination={
          pagination || {
            page: currentPage,
            pageSize: 10,
            pageCount: 1,
            total: commissionHistory.length,
          }
        }
        currentPage={currentPage}
        onPageChange={goToPage}
        isPaginationLoading={isLoading && currentPage !== 1}
      />
    </div>
  );
};

export default History;
