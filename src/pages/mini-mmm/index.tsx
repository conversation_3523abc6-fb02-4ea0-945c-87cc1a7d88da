import React from "react";
// Import images as strings instead of StaticImageData
const img1 = "/mini/img_1.jpg";
const img2 = "/mini/img_2.jpg";
const img3 = "/mini/img_3.jpg";
const img4 = "/mini/img_4.jpg";
const qrCode = "/mini/qr_code.png";

const MiniMMMPage = () => {
  // Image error handler
  const handleImageError = (
    e: React.SyntheticEvent<HTMLImageElement, Event>
  ) => {
    e.currentTarget.src = "/default-image.jpg";
  };

  return (
    <div className="min-h-screen bg-white text-gray-900 font-sans leading-relaxed relative">
      {/* Subtle background pattern */}
      <div className="fixed inset-0 opacity-5 pointer-events-none z-0">
        <div
          className="absolute inset-0 bg-repeat"
          style={{
            backgroundImage: `repeating-linear-gradient(
                 45deg,
                 transparent,
                 transparent 200px,
                 rgba(37, 99, 235, 0.003) 200px,
                 rgba(37, 99, 235, 0.003) 202px
               )`,
          }}
        ></div>
      </div>

      <div className="relative z-10">
        {/* Hero Section */}
        <div className="max-w-4xl mx-auto px-5 py-16">
          <h1 className="text-4xl md:text-5xl font-black text-center mb-3 tracking-tight text-black">
            Hành trình $1,000 đầu tiên với mini MMM
          </h1>
          <p className="text-xl text-center mb-12 text-gray-600 font-normal tracking-wide">
            Đây là lúc có đồng đội và một lộ trình rõ ràng về đích.
          </p>

          {/* Hero Image */}
          <div className="w-full h-96 bg-gray-100 border border-gray-200 rounded-xl mb-14 overflow-hidden">
            <img
              src={img1}
              alt="Team Photo"
              className="w-full h-full object-cover rounded-xl"
              onError={handleImageError}
            />
          </div>

          {/* Content Section */}
          <div className="max-w-3xl mx-auto mb-6">
            <p className="mb-5 text-lg text-gray-700 leading-relaxed">
              Nếu bạn tìm kiếm một người{" "}
              <span className="font-bold text-black bg-gradient-to-b from-transparent from-60% to-yellow-300 to-60% px-1 py-0.5 rounded-sm">
                "thầy"
              </span>{" "}
              và một khoá học thông thường, có lẽ{" "}
              <span className="font-bold text-black bg-gradient-to-b from-transparent from-60% to-yellow-300 to-60% px-1 py-0.5 rounded-sm">
                mini MMM không dành cho bạn.
              </span>
            </p>
            <p className="mb-5 text-lg text-gray-700 leading-relaxed">
              Ở đây, không có{" "}
              <span className="font-bold text-black bg-gradient-to-b from-transparent from-60% to-yellow-300 to-60% px-1 py-0.5 rounded-sm">
                thầy-trò.
              </span>{" "}
              Chỉ có những người{" "}
              <span className="font-bold text-black bg-gradient-to-b from-transparent from-60% to-yellow-300 to-60% px-1 py-0.5 rounded-sm">
                đồng đội.
              </span>{" "}
              Sơn không phải người dạy, Sơn là người{" "}
              <span className="font-bold text-black bg-gradient-to-b from-transparent from-60% to-yellow-300 to-60% px-1 py-0.5 rounded-sm">
                đồng hành.
              </span>
            </p>
            <p className="mb-5 text-lg text-gray-700 leading-relaxed">
              Những gương mặt anh em thấy trên tấm ảnh kia chính là minh chứng.
              Họ từng là những người đồng đội và giờ đây, rất nhiều người trong
              số họ đang là top đầu của MMO Việt Nam. Sơn ở đây, sẽ{" "}
              <span className="font-bold text-black bg-gradient-to-b from-transparent from-60% to-yellow-300 to-60% px-1 py-0.5 rounded-sm">
                không tô vẽ về giấc mơ triệu đô.
              </span>{" "}
              Nhưng có một lời hứa chắc chắn:{" "}
              <span className="font-bold text-black bg-gradient-to-b from-transparent from-60% to-yellow-300 to-60% px-1 py-0.5 rounded-sm">
                "Sơn sẽ cùng anh em chinh phục cột mốc $1,000 đầu tiên".
              </span>
            </p>
            <p className="mb-5 text-lg text-gray-700 leading-relaxed">
              Mục tiêu không chỉ là{" "}
              <span className="font-bold text-black bg-gradient-to-b from-transparent from-60% to-yellow-300 to-60% px-1 py-0.5 rounded-sm">
                tiền.
              </span>{" "}
              Mục tiêu là để anh em{" "}
              <span className="font-bold text-black bg-gradient-to-b from-transparent from-60% to-yellow-300 to-60% px-1 py-0.5 rounded-sm">
                thực sự hiểu
              </span>{" "}
              cuộc chơi này, để có đủ tự tin và bản lĩnh mà tự mình bước tiếp
              những nấc thang tiếp theo. Và đó là lý do Sơn chỉ nhận một nhóm
              vừa đủ, để đồng hành thật sát sao với từng người một.
            </p>
          </div>

          <a
            href="https://docs.google.com/forms/d/e/1FAIpQLSdAUbLr5fIR46QlCdkqpT1yPLXGxyVJu2H-enEGrjWh77V-6g/viewform"
            target="_blank"
            rel="noopener noreferrer"
            className="block w-36 mx-auto my-12 py-3.5 px-9 bg-gradient-to-br from-blue-600 to-blue-700 text-white rounded-lg font-semibold text-base text-center border-none cursor-pointer transition-all duration-300 shadow-lg hover:from-blue-700 hover:to-blue-800 hover:-translate-y-0.5 hover:shadow-xl tracking-wide"
          >
            Đăng ký
          </a>

          {/* Gallery */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-5 my-8">
            <div className="w-full h-48 bg-gray-100 border border-gray-200 rounded-lg overflow-hidden">
              <img
                src={img2}
                alt="Team Activity 1"
                className="w-full h-full object-cover rounded-lg"
                onError={handleImageError}
              />
            </div>
            <div className="w-full h-48 bg-gray-100 border border-gray-200 rounded-lg overflow-hidden">
              <img
                src={img3}
                alt="Team Activity 2"
                className="w-full h-full object-cover rounded-lg"
                onError={handleImageError}
              />
            </div>
            <div className="w-full h-48 bg-gray-100 border border-gray-200 rounded-lg overflow-hidden">
              <img
                src={img4}
                alt="Team Activity 3"
                className="w-full h-full object-cover rounded-lg"
                onError={handleImageError}
              />
            </div>
          </div>
        </div>

        {/* Blue Section */}
        <div className="bg-gradient-to-br from-indigo-900 to-indigo-800 text-white py-14 relative overflow-hidden">
          {/* Pattern overlay */}
          <div
            className="absolute inset-0 opacity-10"
            style={{
              backgroundImage: `linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.02) 30%, rgba(255,255,255,0.02) 32%, transparent 32%),
                                  linear-gradient(-45deg, transparent 30%, rgba(255,255,255,0.01) 30%, rgba(255,255,255,0.01) 32%, transparent 32%)`,
              backgroundSize: "60px 60px",
            }}
          ></div>

          <h2 className="text-2xl md:text-3xl text-center mb-12 px-5 font-bold leading-snug tracking-tight">
            Nếu bạn đang mắc kẹt trong "vòng lặp" mệt mỏi,
            <br />
            Sơn hiểu cảm giác của bạn lúc này...
          </h2>

          <div className="bg-white rounded-2xl p-8 md:p-12 max-w-3xl mx-auto shadow-lg">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {[
                'Hoang mang tột độ giữa một rừng thông tin, bị "ngợp" bởi những lời hứa hẹn "làm giàu nhanh"',
                "Đầu tư rất nhiều thời gian, công sức nhưng làm mãi không ra tiền",
                'Lo sợ thị trường đã "đỏ", đã hết thời. Bạn sợ mình là người đến sau, không còn một cơ hội nào để chen chân vào nữa.',
                'Bạn thiếu một người cố vấn, một người dẫn đường và một người "Gỡ rối" thực sự.',
                'Bạn bị "ám ảnh" bởi thành công của người khác, nó chỉ khiến bạn thêm tự ti, sốt ruột và cảm thấy mình là kẻ thất bại.',
                "Bạn không biết phải viết gì, không tự tin hoặc sợ rằng những gì mình chia sẻ không đủ hay, giá trị để thu hút bất kỳ ai",
              ].map((text, index) => (
                <div key={index} className="flex items-start gap-4">
                  <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center text-white text-sm font-bold flex-shrink-0 mt-0.5">
                    ✕
                  </div>
                  <div className="text-gray-700 leading-relaxed text-sm">
                    <span
                      dangerouslySetInnerHTML={{
                        __html: text.replace(
                          /\*\*(.*?)\*\*/g,
                          "<strong>$1</strong>"
                        ),
                      }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Solution Section */}
        <div className="max-w-4xl mx-auto px-5 py-8">
          <div className="mb-4">
            <p className="text-lg font-semibold text-black mb-2">
              Được rồi. Nếu đó là tất cả vấn đề của bạn, thì đây là{" "}
              <strong className="bg-yellow-300 px-1">giải pháp!</strong>
            </p>

            <div className="mb-0">
              <p className="mb-2">Sơn sẽ cho bạn 2 con đường:</p>

              <div className="mb-1.5">
                <span>
                  <strong>Một là</strong> những thị trường ngách{" "}
                  <strong>dễ thở</strong> cho người mới.
                </span>
              </div>

              <div className="mb-1.5">
                <span>
                  <strong>Hai là</strong> tấm vé vào thế giới Finance & Crypto,
                  nơi tên tuổi của Sơn Piaz và mạng lưới các master sẽ là{" "}
                  <strong>"vũ khí"</strong> mạnh nhất của bạn.
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Not for Everyone Section */}
        <div className="max-w-3xl mx-auto px-5 mt-4">
          <div className="bg-gradient-to-br from-blue-50 to-white backdrop-blur-sm rounded-2xl p-8 md:p-11 shadow-lg border border-blue-100 mb-4">
            <h2 className="text-2xl md:text-3xl font-bold text-center mb-10 text-black leading-tight">
              Đây Không Phải Chương Trình Dành
              <br />
              Cho Tất Cả Mọi Người
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {[
                "Người muốn <strong>học sâu</strong> hơn sau chuỗi chương trình <strong>Affiliate x AI</strong> mà Sơn chia sẻ ở Chuỗi đào tạo 6 buổi.",
                "Người muốn phát triển <strong>Sự nghiệp dài hạn</strong> với Affiliate Marketing trong nhiều năm tới (3- 5 - 10 năm tiếp theo)",
                "Người muốn có thêm <strong>mạng lưới Mối quan hệ</strong>, cộng đồng Mastermind <strong>chất lượng</strong>, cùng giúp nhau tiến lên",
                "Người tin vào tương lai của <strong>công nghệ & AI</strong> sẽ tạo cơ hội lớn cho những người đi sớm",
              ].map((text, index) => (
                <div key={index} className="flex items-start gap-4">
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-bold flex-shrink-0 mt-0.5">
                    ✓
                  </div>
                  <div className="text-gray-700 leading-relaxed text-sm">
                    <span dangerouslySetInnerHTML={{ __html: text }} />
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="text-center my-8">
            <p className="mb-5 text-base text-gray-700 leading-relaxed">
              Nếu bạn gật đầu với tất cả những điều trên, thì chúng ta có{" "}
              <strong>cùng một đích đến.</strong>
            </p>
            <p className="mb-5 text-base text-gray-700 leading-relaxed">
              Và trong Mini-MMM, chúng ta sẽ không <strong>"hy vọng"</strong>{" "}
              kiếm được <strong>$1,000.</strong> Chúng ta sẽ lên kế hoạch và{" "}
              <strong>thực thi</strong> nó. Mục tiêu đó phải được hoàn thành
              trong <strong>vài tuần, không phải vài năm.</strong>
            </p>
          </div>

          <a
            href="https://docs.google.com/forms/d/e/1FAIpQLSdAUbLr5fIR46QlCdkqpT1yPLXGxyVJu2H-enEGrjWh77V-6g/viewform"
            target="_blank"
            rel="noopener noreferrer"
            className="block w-36 mx-auto my-12 py-3.5 px-9 bg-gradient-to-br from-blue-600 to-blue-700 text-white rounded-lg font-semibold text-base text-center border-none cursor-pointer transition-all duration-300 shadow-lg hover:from-blue-700 hover:to-blue-800 hover:-translate-y-0.5 hover:shadow-xl tracking-wide"
          >
            Đăng Ký
          </a>
        </div>

        {/* Affiliate Truth Section */}
        <div className="max-w-4xl mx-auto px-5 mt-4 text-center py-6">
          <h2 className="text-2xl md:text-3xl font-bold mb-8 text-black">
            Sự Thật Về Sân Chơi Affiliate
          </h2>

          <div className="flex justify-center items-center gap-8 mb-8 flex-wrap">
            {[
              { number: "#1", title: "Nội địa", color: "text-red-500" },
              { number: "#2", title: "Nhập khẩu", color: "text-orange-500" },
              { number: "#3", title: "Xuất khẩu", color: "text-lime-500" },
              { number: "#4", title: "Bán quốc tế", color: "text-green-500" },
            ].map((level, index) => (
              <div
                key={index}
                className="bg-gradient-to-br from-blue-50 to-blue-25 rounded-2xl p-4 px-6 border border-blue-100 transition-all duration-300 min-w-30 text-center relative overflow-hidden shadow-sm hover:shadow-md hover:-translate-y-1"
              >
                <div
                  className={`text-sm font-bold mb-1 tracking-wider ${level.color}`}
                >
                  {level.number}
                </div>
                <h3 className="text-sm font-semibold text-slate-800 tracking-wide">
                  {level.title}
                </h3>
              </div>
            ))}
          </div>

          <div className="max-w-3xl mx-auto text-left">
            <p className="mb-4 text-base text-gray-700 leading-relaxed">
              Hầu hết mọi người đang chen chúc ở{" "}
              <strong>Cấp 1 & 2 (Thị trường trong nước)</strong>
            </p>
            <p className="mb-4 text-base text-gray-700 leading-relaxed">
              NHƯNG trong khi toàn bộ 'miếng bánh' lớn nhất, màu mỡ nhất lại nằm
              ở <strong>Cấp 4 - Thị trường Quốc tế</strong>{" "}
              <em className="text-gray-600">
                (lấy sản phẩm nước ngoài, bán sang thị trường nước ngoài)
              </em>
            </p>
            <p className="mb-4 text-base text-gray-700 leading-relaxed">
              Sơn không muốn giữ bạn ở 'ao làng', Mini-MMM sẽ trang bị để bạn{" "}
              <strong>vươn ra đại dương.</strong>
            </p>
            <p className="mb-4 text-base text-gray-700 leading-relaxed">
              <strong>Đó là sân chơi duy nhất chúng ta sẽ tập trung.</strong>
            </p>
          </div>
        </div>

        {/* Program Details Section */}
        <div className="max-w-4xl mx-auto px-5 mt-4">
          <h2 className="text-2xl md:text-3xl font-bold text-center mb-8 text-black leading-tight">
            Đây không phải là một khoá học. Đây là 8 TUẦN
            <br />
            sẽ tái định hình lại TƯƠNG LAI của bạn
          </h2>

          {/* Knowledge Section */}
          <div className="bg-gradient-to-br from-blue-50 to-white backdrop-blur-sm rounded-2xl p-8 md:p-9 mb-4 shadow-lg border border-blue-100 relative overflow-hidden">
            <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-400 to-transparent"></div>

            <h3 className="text-xl font-bold text-black mb-6">KIẾN THỨC:</h3>

            <div className="mb-8">
              {[
                "<strong>Lộ trình học thực chiến trong 8 tuần:</strong> Mỗi tuần 1 buổi học chính + 1 buổi phụ đạo để đảm bảo bạn đi vào thực tế, hành động 'Học 1 - Làm 10'",
                "<strong>Làm chủ 13+ phương pháp Organic Traffic</strong> đỉnh cao: Kéo traffic miễn phí bền vững ngay cả khi bạn không muốn xuất hiện",
                "<strong>Chinh phục 14+ kênh traffic quốc tế đã có case study thành công.</strong>",
                "<strong>5 phương pháp tạo trang chuyển đổi hiệu quả cao,</strong> từ đơn giản đến phức tạp",
                "<strong>Cách xây dựng Website Listing dự án Affiliate AI bằng Vibe Code</strong> (không cần biết lập trình)",
              ].map((text, index) => (
                <div
                  key={index}
                  className="flex items-start gap-4 mb-5 last:mb-0"
                >
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-bold flex-shrink-0 mt-0.5">
                    ✓
                  </div>
                  <div className="text-gray-700 leading-relaxed text-sm">
                    <span dangerouslySetInnerHTML={{ __html: text }} />
                  </div>
                </div>
              ))}
            </div>

            <div className="bg-gradient-to-br from-red-25 to-red-10 rounded-xl p-6 my-8 border-l-4 border-red-500">
              <div className="text-base font-bold text-black mb-5 flex items-center gap-2">
                <span className="text-lg">🔥</span>
                <strong>Chuyên đề ĐẶC BIỆT + NÂNG CAO</strong>
              </div>

              {[
                "<strong>Bí quyết đàm phán X2 hoa hồng</strong> với các nhà cung cấp.",
                '<strong>Tư duy xây dựng một "đế chế" trên Internet,</strong> không chỉ là một business nhỏ lẻ',
              ].map((text, index) => (
                <div
                  key={index}
                  className="flex items-start gap-4 mb-5 last:mb-0"
                >
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-bold flex-shrink-0 mt-0.5">
                    ✓
                  </div>
                  <div className="text-gray-700 leading-relaxed text-sm">
                    <span dangerouslySetInnerHTML={{ __html: text }} />
                  </div>
                </div>
              ))}
            </div>

            <div className="bg-gradient-to-br from-blue-25 to-blue-10 rounded-xl p-6 border-l-4 border-blue-600 mt-8">
              <p className="mb-3 text-gray-700 leading-relaxed text-sm">
                Và Sơn thấy nhu cầu của anh em về{" "}
                <strong>ngách Finance & Crypto</strong> vẫn là rất lớn.
              </p>
              <p className="mb-3 text-gray-700 leading-relaxed text-sm">
                Và may mắn thay, đây lại đúng là <strong>'sân nhà'</strong> của
                Sơn - lĩnh vực đã tạo nên thương hiệu Sơn Piaz.
              </p>
              <p className="mb-0 text-gray-700 leading-relaxed text-sm">
                Vậy nên, Sơn sẽ có <strong>2 buổi chuyên sâu,</strong> không chỉ
                để hiểu, mà để chinh phục hai thị trường khó nhằn nhưng màu mỡ
                nhất. Một điều Sơn có thể <strong>tự tin nói:</strong> Rất nhiều
                'Master' về mảng này bạn thấy ngoài kia, ít nhiều cũng đã từng
                ngồi trong một khoá MMM của Sơn.
              </p>
            </div>
          </div>

          {/* Support Section */}
          <div className="bg-gradient-to-br from-blue-50 to-white backdrop-blur-sm rounded-2xl p-8 md:p-9 mb-4 shadow-lg border border-blue-100 relative overflow-hidden">
            <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-400 to-transparent"></div>

            <h3 className="text-xl font-bold text-black mb-6">SUPPORT:</h3>

            <div className="text-gray-700 leading-relaxed text-sm mb-6 p-5 bg-blue-25 rounded-lg border-l-4 border-blue-600">
              Sơn biết lý do lớn nhất khiến{" "}
              <strong>99% người học online thất bại</strong> là gì:{" "}
              <strong>Không có ai ở đó khi họ cần nhất.</strong> Kiến thức dù
              hay đến mấy cũng trở nên vô nghĩa khi bạn gặp một lỗi nhỏ mà không
              biết hỏi ai.
            </div>

            {[
              '<strong>Một nơi an toàn để hỏi:</strong> Sẽ không có bất kỳ câu hỏi nào của bạn bị bỏ qua, dù là "ngớ ngẩn" nhất. Bạn "kẹt" ở đâu, sẽ có Sơn, team và cả cộng đồng ở đó để "gỡ" cùng bạn. Tuyệt đối <strong>không có chuyện bị "bơ"</strong>',
              '<strong>Và đây là "đặc sản" chỉ có tại MMM...</strong> Hỗ trợ qua trực tiếp. Điều này có nghĩa là, Sơn sẽ không chỉ nói cho bạn phải làm gì. Sơn hoặc team support sẽ vào tận máy tính của bạn, cầm chuột và làm cùng bạn cho đến khi vấn đề được giải quyết triệt để. Đây là sự hỗ trợ ở cấp độ sâu nhất, là sự tận tâm mà bạn sẽ không tìm thấy ở bất cứ đâu.',
            ].map((text, index) => (
              <div
                key={index}
                className="flex items-start gap-4 mb-5 last:mb-0"
              >
                <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-bold flex-shrink-0 mt-0.5">
                  ✓
                </div>
                <div className="text-gray-700 leading-relaxed text-sm">
                  <span dangerouslySetInnerHTML={{ __html: text }} />
                </div>
              </div>
            ))}
          </div>

          {/* Network Section */}
          <div className="bg-gradient-to-br from-blue-50 to-white backdrop-blur-sm rounded-2xl p-8 md:p-9 mb-4 shadow-lg border border-blue-100 relative overflow-hidden">
            <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-400 to-transparent"></div>

            <h3 className="text-xl font-bold text-black mb-6">
              KẾT NỐI & MỐI QUAN HỆ:
            </h3>

            <div className="text-gray-700 leading-relaxed text-sm mb-6 p-5 bg-blue-25 rounded-lg border-l-4 border-blue-600">
              Sơn luôn tâm niệm, thành công lớn nhất của mình không phải là kiếm
              được bao nhiêu tiền, mà là giờ đây, khi mình chia sẻ, vẫn có những{" "}
              <strong>anh em đồng đội cũ ủng hộ.</strong> Mini-MMM được tạo ra
              để tiếp nối điều đó.
            </div>

            {[
              '<strong>Trở thành "người trong cuộc":</strong> Bạn không chỉ kết nối với Sơn, mà chính thức gia nhập vào một mạng lưới những người đồng đội thành công, những người sẵn sàng hỗ trợ và kéo bạn đi lên.',
              "<strong>Tấm vé tham dự Private Mastermind thường niên:</strong> Đây là đặc quyền chỉ dành riêng cho thành viên MMM. Một sự kiện kín, nơi những anh em top thị trường trong mảng MMO hội tụ để chia sẻ những cuộc chơi lớn nhất. Đây là nơi vị thế của bạn được nâng lên một tầm cao mới.",
            ].map((text, index) => (
              <div
                key={index}
                className="flex items-start gap-4 mb-5 last:mb-0"
              >
                <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-bold flex-shrink-0 mt-0.5">
                  ✓
                </div>
                <div className="text-gray-700 leading-relaxed text-sm">
                  <span dangerouslySetInnerHTML={{ __html: text }} />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Final Commitment Section */}
        <div className="max-w-3xl mx-auto px-5 mt-12 text-center">
          <h2 className="text-2xl md:text-3xl font-bold text-black mb-8 leading-tight">
            Và Cuối Cùng, Sơn Cần Ở Bạn Một Sự Cam Kết...
          </h2>

          <div className="text-left mb-8">
            <p className="mb-4 text-gray-700 leading-relaxed text-base">
              Vì tất cả những giá trị trên, Sơn{" "}
              <strong>không tìm kiếm số lượng.</strong> Sơn tìm kiếm những người{" "}
              <strong>đồng đội sẵn sàng cam kết hành động.</strong>
            </p>
            <p className="mb-4 text-gray-700 leading-relaxed text-base">
              Đây là một cuộc chơi hai chiều!
            </p>
            <p className="mb-4 text-gray-700 leading-relaxed text-base">
              Bạn đến đây không phải chỉ để nhận, mà còn để nỗ lực.
            </p>
            <p className="mb-4 text-gray-700 leading-relaxed text-base">
              <strong>
                Một là bạn đã "chất", hai là bạn có khao khát cháy bỏng để trở
                nên "chất".
              </strong>
            </p>
            <p className="mb-4 text-gray-700 leading-relaxed text-base">
              Nếu không, chúng ta rất khó để đi cùng nhau trên một chặng đường
              dài.
            </p>
          </div>
        </div>

        {/* Pricing Section */}
        <div className="text-center my-12">
          <h2 className="text-2xl font-bold text-black mb-3">
            Học phí hiện tại:{" "}
            <span className="text-red-500 font-black">35.976.000 VNĐ</span>{" "}
            <span className="text-gray-600 font-medium">
              (giá này 23/6 - 25/6)
            </span>
          </h2>
          <p className="text-base text-gray-600 italic">
            Cứ sau 3 ngày, giá sẽ tăng thêm 2.000.000 VNĐ
          </p>
        </div>

        {/* Registration Methods */}
        <div className="bg-gradient-to-br from-amber-900 to-red-800 text-white p-9 my-8 rounded-2xl relative max-w-4xl mx-auto shadow-lg">
          <div
            className="absolute inset-0 opacity-10"
            style={{
              backgroundImage: `linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.02) 30%, rgba(255,255,255,0.02) 32%, transparent 32%)`,
              backgroundSize: "30px 30px",
            }}
          ></div>

          <h2 className="text-2xl md:text-3xl font-bold text-center mb-10 text-white">
            THÔNG TIN CHUYỂN KHOẢN
          </h2>

          <div className="flex items-center gap-10 flex-wrap">
            <div className="flex-1 min-w-72">
              <h3 className="text-xl font-bold text-white mb-6">
                Techcombank: Nguyễn Tùng Sơn
              </h3>
              <div className="space-y-3">
                <p className="text-base leading-snug">
                  <span className="text-yellow-300 font-semibold inline-block w-30">
                    STK:
                  </span>
                  <span className="text-gray-200">**************</span>
                </p>
                <p className="text-base leading-snug">
                  <span className="text-yellow-300 font-semibold inline-block w-30">
                    Số tiền:
                  </span>
                  <span className="text-gray-200">35.976.000đ/học viên</span>
                </p>
                <p className="text-base leading-snug">
                  <span className="text-yellow-300 font-semibold inline-block w-30">
                    Nội dung CK:
                  </span>
                  <span className="text-gray-200">Họ tên + Số điện thoại</span>
                </p>
              </div>
            </div>

            <div className="flex-shrink-0">
              <div className="w-44 h-44 bg-white rounded-xl border-4 border-yellow-300 overflow-hidden p-2">
                <img
                  src={qrCode}
                  alt="QR Code - Banking"
                  className="w-full h-full object-contain rounded-lg"
                  onError={(e) => {
                    e.currentTarget.src = "/default-qr.png";
                  }}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Bonus Section */}
        <div className="my-12 text-center">
          <p className="text-base text-black mb-3 leading-relaxed">
            <strong>
              Ưu đãi dành cho thành viên Mini - MMM: 1 năm sử dụng Dịch vụ Pro
              Plan: trị giá $228 ~ 5.902.000 VND
            </strong>
          </p>
          <p className="text-sm text-gray-600 leading-snug">
            Kèm theo Video hướng dẫn sử dụng, các cập nhật mới về Affiliate x AI
          </p>
        </div>

        {/* Final Message */}
        <div className="text-center my-8 p-6 bg-gradient-to-br from-blue-25 to-blue-10 rounded-xl">
          <p className="text-lg text-blue-600 font-semibold italic">
            Hẹn gặp lại bạn tại Mini - MMM 2025
          </p>
        </div>
      </div>
    </div>
  );
};

export default MiniMMMPage;
