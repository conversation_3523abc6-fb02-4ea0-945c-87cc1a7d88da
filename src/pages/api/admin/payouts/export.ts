import { NextApiRequest, NextApiResponse } from "next";
import { StrapiAdminClient } from "@/utils/request";
import { sendApiError } from "@/utils/api-error-handler";
import { createApiContext } from "@/utils/api-middleware";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const { method } = req;

  if (method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    // Use centralized context with required auth
    const { token } = createApiContext(req, { requireAuth: true });

    // Extract query parameters
    const { status, format, search } = req.query;

    const query = {
      status: status as "pending" | "approved" | "completed" | undefined,
      format: (format as "csv" | "excel") || "csv",
      search: search as string | undefined,
    };

    // Call StrapiAdminClient to export payouts
    const data = await StrapiAdminClient.exportPayouts(query, token!);

    // Set appropriate headers for file download
    const filename = `payouts_${query.status || "all"}_${
      new Date().toISOString().split("T")[0]
    }.${query.format}`;
    const contentType =
      query.format === "excel"
        ? "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        : "text/csv";

    res.setHeader("Content-Type", contentType);
    res.setHeader("Content-Disposition", `attachment; filename="${filename}"`);
    res.setHeader("Cache-Control", "no-cache");

    // Send the file data
    res.status(200).send(data);
  } catch (error: any) {
    console.error("Export payouts API error:", error);
    sendApiError(res, error, "Error exporting payouts");
  }
}
