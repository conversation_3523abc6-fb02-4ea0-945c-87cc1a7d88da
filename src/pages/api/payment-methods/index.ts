import { AppError, IPaymentMethodDetail, PaymentMethodsResponse } from "@/interfaces";
import { StrapiClient } from "@/utils/request";
import type { NextApiRequest, NextApiResponse } from "next";
import { sendApiError } from "@/utils/api-error-handler";

// Sample fallback data for development/testing
const fallbackData: PaymentMethodsResponse = {
  data: [
    {
      id: 1,
      documentId: "fallback-payment-1",
      name: "Credit Card",
      createdAt: "2024-01-01T00:00:00.000Z",
      updatedAt: "2024-01-01T00:00:00.000Z",
      publishedAt: "2024-01-01T00:00:00.000Z"
    },
    {
      id: 2,
      documentId: "fallback-payment-2",
      name: "PayPal",
      createdAt: "2024-01-01T00:00:00.000Z",
      updatedAt: "2024-01-01T00:00:00.000Z",
      publishedAt: "2024-01-01T00:00:00.000Z"
    }
  ],
  meta: {
    pagination: {
      page: 1,
      pageSize: 25,
      pageCount: 1,
      total: 2
    }
  }
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<PaymentMethodsResponse | AppError>
) {
  try {
    const response: any = await StrapiClient.getPaymentMethods();
    
    res.status(200).json(response);
  } catch (error: any) {
    console.error("Error fetching payment methods:", error);
    sendApiError(res, error, "Error fetching payment methods");
  }
}
