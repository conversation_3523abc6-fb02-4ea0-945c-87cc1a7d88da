import { Provider } from "react-redux";
import type { AppProps } from "next/app";
import { useRouter } from "next/router";
import { wrapper } from "@/store";
import { ThemeProvider } from "next-themes";
import "@/styles/globals.css";
import "@/styles/yoopta-notion.css";
import { AiChatBot, Header, AiScript } from "@/components";
import { ToastContextProvider } from "@/context/ToastContext";
import ToastObserver from "@/components/ToastObserver";
import ReferralTracker from "@/components/ReferralTracker";

export default function App({ Component, ...rest }: AppProps) {
  const { store, props } = wrapper.useWrappedStore(rest);
  const router = useRouter();

  // Hide header on specific pages
  const hideHeaderPages = ["/ad", "/admin", '/editor'];
  const shouldHideHeader =
    hideHeaderPages.some((page) => router.pathname.includes(page)) ||
    (router.pathname === "/admin" &&
      typeof window !== "undefined" &&
      window.location.hash.startsWith("#payout"));

  return (
    <Provider store={store}>
      <ToastContextProvider>
        <ToastObserver />
        <ReferralTracker />
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          {!shouldHideHeader && <Header />}
          <div className={shouldHideHeader ? "" : "mt-[75px]"}>
            <Component {...props} />
          </div>
          {/* <AiChatBot /> */}
          <AiScript />
        </ThemeProvider>
      </ToastContextProvider>
    </Provider>
  );
}
