"use client";

import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { AppDispatch } from "@/store";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON>T<PERSON>le,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Loader2,
  DollarSign,
  CreditCard,
  Building2, ChevronDown
} from "lucide-react";
import { actions as payoutActions } from "@/features/payout/payout.slice";
import { actions as adminActions } from "@/features/admin/admin.slice";
import {
  selectAdminPartners,
  selectAdminPartnersLoading,
  selectAdminPartnersError,
  selectAdminSettings,
  selectAdminSettingsLoading,
  selectAdminSettingsError,
} from "@/features/admin/admin.slice";
import {
  selectAdminPayoutsLoading,
  selectCreatePayoutError,
  selectAdminPayoutsSuccess,
} from "@/features/payout/payout.slice";

interface PayoutFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

interface PayoutFormData {
  partnerId: string;
  amount: string;
  method: "paypal" | "bank transfer" | "";
  payout_status: "pending" | "approved" | "completed";
}

interface FormErrors {
  partnerId?: string;
  amount?: string;
  method?: string;
  payout_status?: string;
  general?: string;
}

interface PartnerSearchResult {
  id: number;
  documentId: string;
  referral_code: string;
  referrer_status: string;
  user: {
    id: number;
    email: string;
    first_name: string;
    last_name: string;
    username: string;
  };
  balance?: number; // Available balance for payout from partner.balance
}

export default function PayoutFormModal({
  isOpen,
  onClose,
  onSuccess,
}: PayoutFormModalProps) {
  const dispatch = useDispatch<AppDispatch>();

  // Redux state
  const partners = useSelector(selectAdminPartners);
  const partnersLoading = useSelector(selectAdminPartnersLoading);
  const partnersError = useSelector(selectAdminPartnersError);
  const payoutLoading = useSelector(selectAdminPayoutsLoading);
  const payoutError = useSelector(selectCreatePayoutError);
  const payoutSuccess = useSelector(selectAdminPayoutsSuccess);

  // Admin settings state
  const adminSettings = useSelector(selectAdminSettings);
  const adminSettingsLoading = useSelector(selectAdminSettingsLoading);
  const adminSettingsError = useSelector(selectAdminSettingsError);

  // Form state
  const [formData, setFormData] = useState<PayoutFormData>({
    partnerId: "",
    amount: "",
    method: "",
    payout_status: "pending", // Default to pending as per requirement
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Partner search state
  const [partnerSearchOpen, setPartnerSearchOpen] = useState(false);
  const [partnerSearchQuery, setPartnerSearchQuery] = useState("");
  const [selectedPartner, setSelectedPartner] =
    useState<PartnerSearchResult | null>(null);

  // Ref for partner search dropdown
  const partnerDropdownRef = useRef<HTMLDivElement>(null);

  // Hide partner select dropdown on outside click
  useEffect(() => {
    if (!partnerSearchOpen) return;
    function handleClickOutside(event: MouseEvent) {
      if (
        partnerDropdownRef.current &&
        !partnerDropdownRef.current.contains(event.target as Node)
      ) {
        setPartnerSearchOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [partnerSearchOpen]);

  // Load partners and admin settings when modal opens and clear create payout error
  useEffect(() => {
    if (isOpen) {
      // Clear any previous create payout errors when modal opens
      dispatch(payoutActions.clearCreatePayoutError());

      // Fetch admin settings if not already loaded
      if (!adminSettings && !adminSettingsLoading) {
        dispatch(adminActions.fetchSettings());
      }

      if (partners.length === 0 && !partnersLoading) {
        dispatch(
          adminActions.fetchPartners({
            page: 1,
            pageSize: 100,
            status: "active",
          })
        );
      }
    }
  }, [isOpen, partners.length, partnersLoading, adminSettings, adminSettingsLoading, dispatch]);

  // Handle success/error states
  useEffect(() => {
    if (payoutSuccess && isSubmitting) {
      setIsSubmitting(false);
      onSuccess?.(); // Trigger refresh in parent component
      handleClose();
    }
  }, [payoutSuccess, isSubmitting, onSuccess]);

  useEffect(() => {
    if (payoutError && isSubmitting) {
      setIsSubmitting(false);
      setErrors({ general: payoutError });
    }
  }, [payoutError, isSubmitting]);

  // Debounced partner search using Redux dispatch
  useEffect(() => {
    // Don't search for very short queries
    if (partnerSearchQuery.length > 0 && partnerSearchQuery.length < 2) {
      return;
    }

    // Debounce the search to avoid too many API calls
    const timer = setTimeout(() => {
      dispatch(
        adminActions.fetchPartners({
          page: 1,
          pageSize: 10,
          search: partnerSearchQuery,
          status: "active",
        })
      );
    }, 300); // 300ms debounce

    return () => clearTimeout(timer);
  }, [partnerSearchQuery, dispatch]);

  const handleClose = () => {
    setFormData({
      partnerId: "",
      amount: "",
      method: "",
      payout_status: "pending",
    });
    setErrors({});
    setIsSubmitting(false);
    setSelectedPartner(null);
    setPartnerSearchQuery("");
    dispatch(payoutActions.clearPayoutSuccess());
    dispatch(payoutActions.clearCreatePayoutError());
    onClose();
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.partnerId) {
      newErrors.partnerId = "Please select a partner";
    }

    if (!formData.amount) {
      newErrors.amount = "Amount is required";
    } else {
      const amount = parseFloat(formData.amount);
      const minimumPayout = adminSettings?.minimumPayout || 50; // Default to $50 if settings not loaded

      if (isNaN(amount) || amount <= 0) {
        newErrors.amount = "Amount must be a positive number";
      } else if (amount < minimumPayout) {
        newErrors.amount = `Minimum payout amount is $${minimumPayout.toFixed(2)}`;
      } else if (selectedPartner && amount > (selectedPartner.balance || 0)) {
        newErrors.amount = `Amount cannot exceed partner's available balance ($${
          selectedPartner.balance || 0
        })`;
      }
    }

    if (!formData.method) {
      newErrors.method = "Please select a payment method";
    }

    if (!formData.payout_status) {
      newErrors.payout_status = "Please select a payout status";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    dispatch(
      payoutActions.createPayoutRequest({
        partnerId: formData.partnerId,
        amount: parseFloat(formData.amount),
        method: formData.method as "paypal" | "bank transfer",
        payout_status: formData.payout_status,
      })
    );
  };

  const handleInputChange = (field: keyof PayoutFormData, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: undefined,
      }));
    }
  };

  const getPartnerDisplayName = (partner: {
    user: {
      first_name?: string;
      last_name?: string;
      username?: string;
      email: string;
    };
  }) => {
    if (!partner.user) {
      return "Unknown Partner";
    }
    const { first_name, last_name, username, email } = partner.user;
    if (first_name && last_name) {
      return `${first_name} ${last_name} (${email})`;
    }
    return `${username || email} (${email})`;
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md bg-white dark:bg-[#181a20] border border-gray-100 dark:border-[#23262f]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-gray-900 dark:text-gray-100">
            <DollarSign className="h-5 w-5 text-green-600" />
            Create New Payout
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Partner Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Partner *
            </label>
            {/* Wrap the dropdown and button in a relative div for outside click detection */}
            <div className="space-y-2 relative" ref={partnerDropdownRef}>
              <Button
                type="button"
                variant="outline"
                className={`w-full justify-between ${
                  errors.partnerId ? "border-red-500" : ""
                } bg-white dark:bg-[#23262f] text-gray-900 dark:text-gray-100 border border-gray-300 dark:border-[#353945]`}
                disabled={isSubmitting}
                onClick={() => {
                  setPartnerSearchOpen(!partnerSearchOpen);
                }}
              >
                {selectedPartner
                  ? getPartnerDisplayName(selectedPartner)
                  : "Search and select a partner..."}
                <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              </Button>

              {partnerSearchOpen && (
                <div className="absolute z-50 w-full mt-1 bg-white dark:bg-[#23262f] border border-gray-200 dark:border-[#353945] rounded-md shadow-lg max-h-60 overflow-auto">
                  <div className="p-2">
                    <input
                      type="text"
                      placeholder="Search partners..."
                      value={partnerSearchQuery}
                      onChange={(e) => setPartnerSearchQuery(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-[#353945] rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-[#181a20] text-gray-900 dark:text-gray-100"
                    />
                  </div>
                  <div className="max-h-40 overflow-y-auto">
                    {partnersLoading ? (
                      <div className="flex items-center gap-2 justify-center py-4 text-gray-700 dark:text-gray-300">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Loading...
                      </div>
                    ) : partners.length === 0 ? (
                      <div className="py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                        No partners found
                      </div>
                    ) : (
                      partners.map((partner: any) => (
                        <div
                          key={partner.documentId}
                          className="px-3 py-2 hover:bg-gray-100 dark:hover:bg-[#23262f] cursor-pointer"
                          onClick={() => {
                            const partnerWithBalance = {
                              ...partner,
                              balance: partner.balance || 0,
                            };
                            setSelectedPartner(partnerWithBalance);
                            setFormData((prev) => ({
                              ...prev,
                              partnerId: partner.documentId,
                            }));
                            setPartnerSearchOpen(false);
                            if (errors.partnerId) {
                              setErrors((prev) => ({
                                ...prev,
                                partnerId: undefined,
                              }));
                            }
                          }}
                        >
                          <div className="flex flex-col">
                            <span className="text-sm text-gray-900 dark:text-gray-100">
                              {getPartnerDisplayName(partner)}
                            </span>
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              Balance: $
                              {(partner.balance || 0).toFixed(2)}
                            </span>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </div>
              )}
            </div>
            {selectedPartner && (
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Available Balance:{" "}
                <span className="font-medium text-green-600 dark:text-green-400">
                  ${selectedPartner.balance?.toFixed(2) || "0.00"}
                </span>
              </div>
            )}
            {errors.partnerId && (
              <p className="text-sm text-red-600">{errors.partnerId}</p>
            )}
          </div>

          {/* Amount Input */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Amount (USD) *
            </label>
            <div className="relative">
              <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="number"
                step="0.01"
                min={adminSettings?.minimumPayout?.toString() || "50"}
                placeholder="0.00"
                value={formData.amount}
                onChange={(e) => handleInputChange("amount", e.target.value)}
                className={`pl-10 ${
                  errors.amount ? "border-red-500" : ""
                } bg-white dark:bg-[#23262f] text-gray-900 dark:text-gray-100 border border-gray-300 dark:border-[#353945]`}
                disabled={isSubmitting}
              />
            </div>

            {/* Payout policy information */}
            {adminSettings && (
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 px-3 py-2 rounded-md">
                  <div className="w-1.5 h-1.5 rounded-full bg-blue-600 dark:bg-blue-400"></div>
                  <span>
                    Minimum payout amount: ${adminSettings.minimumPayout.toFixed(2)}
                  </span>
                </div>
                <div className="flex items-center gap-2 text-sm text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 px-3 py-2 rounded-md">
                  <div className="w-1.5 h-1.5 rounded-full bg-blue-600 dark:bg-blue-400"></div>
                  <span>
                    Processing fee: ${adminSettings.processingFee.toFixed(2)}
                  </span>
                </div>
              </div>
            )}

            {/* Loading state for admin settings */}
            {adminSettingsLoading && (
              <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                <Loader2 className="h-3 w-3 animate-spin" />
                <span>Loading payout settings...</span>
              </div>
            )}

            {/* Error state for admin settings */}
            {adminSettingsError && (
              <div className="text-sm text-amber-600 dark:text-amber-400 bg-amber-50 dark:bg-amber-900/20 px-3 py-2 rounded-md">
                Unable to load payout settings. Using defaults: minimum $50.00, processing fee $2.50
              </div>
            )}

            {errors.amount && (
              <p className="text-sm text-red-600">{errors.amount}</p>
            )}
          </div>

          {/* Payment Method Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Payment Method *
            </label>
            <Select
              value={formData.method}
              onValueChange={(value) => handleInputChange("method", value)}
              disabled={isSubmitting}
            >
              <SelectTrigger
                className={`${
                  errors.method ? "border-red-500" : ""
                } bg-white dark:bg-[#23262f] text-gray-900 dark:text-gray-100 border border-gray-300 dark:border-[#353945]`}
              >
                <SelectValue placeholder="Select payment method" />
              </SelectTrigger>
              <SelectContent className="bg-white dark:bg-[#23262f] text-gray-900 dark:text-gray-100 border border-gray-200 dark:border-[#353945]">
                <SelectItem value="paypal">
                  <div className="flex items-center gap-2">
                    <CreditCard className="h-4 w-4" />
                    PayPal
                  </div>
                </SelectItem>
                <SelectItem value="bank transfer">
                  <div className="flex items-center gap-2">
                    <Building2 className="h-4 w-4" />
                    Bank Transfer
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
            {errors.method && (
              <p className="text-sm text-red-600">{errors.method}</p>
            )}
          </div>

          {/* Payout Status Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Payout Status *
            </label>
            <Select
              value={formData.payout_status}
              onValueChange={(value) =>
                handleInputChange("payout_status", value)
              }
              disabled={isSubmitting}
            >
              <SelectTrigger
                className={`${
                  errors.payout_status ? "border-red-500" : ""
                } bg-white dark:bg-[#23262f] text-gray-900 dark:text-gray-100 border border-gray-300 dark:border-[#353945]`}
              >
                <SelectValue placeholder="Select payout status" />
              </SelectTrigger>
              <SelectContent className="bg-white dark:bg-[#23262f] text-gray-900 dark:text-gray-100 border border-gray-200 dark:border-[#353945]">
                <SelectItem value="pending">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                    Pending
                  </div>
                </SelectItem>
                <SelectItem value="approved">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 rounded-full bg-green-500"></div>
                    Approved
                  </div>
                </SelectItem>
                <SelectItem value="completed">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                    Completed
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
            {errors.payout_status && (
              <p className="text-sm text-red-600">{errors.payout_status}</p>
            )}
          </div>

          {/* General Error */}
          {errors.general && (
            <div className="p-3 bg-red-50 dark:bg-[#2e1a1a] border border-red-200 dark:border-red-700 rounded-md">
              <p className="text-sm text-red-600">{errors.general}</p>
            </div>
          )}

          {/* Partners Error */}
          {partnersError && (
            <div className="p-3 bg-red-50 dark:bg-[#2e1a1a] border border-red-200 dark:border-red-700 rounded-md">
              <p className="text-sm text-red-600">
                Failed to load partners: {partnersError}
              </p>
            </div>
          )}
        </form>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={isSubmitting}
            className="bg-white dark:bg-[#23262f] text-gray-900 dark:text-gray-100 border border-gray-300 dark:border-[#353945]"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            onClick={handleSubmit}
            disabled={isSubmitting || partnersLoading || payoutLoading}
            className="bg-green-600 hover:bg-green-700 text-white dark:bg-green-600 dark:hover:bg-green-700"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Creating...
              </>
            ) : (
              <>
                <DollarSign className="mr-2 h-4 w-4" />
                Create Payout
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
