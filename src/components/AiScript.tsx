"use client";

import { useState, useEffect, useRef } from "react";
import { Co<PERSON>, Send, Sparkles } from "lucide-react";
import { useDispatch, useSelector } from "react-redux";
import { actions } from "@/features/aiscript/aiscript.slice";
import { actions as socialActions } from "@/features/social-listening/social-listening.slice";
import {
  selectAiscriptMessages,
  selectAiscriptLoading,
  selectAiscriptOpen,
} from "@/features/aiscript/aiscript.slice";
import { selectIsAuthenticated } from "@/features/auth/auth.slice";
import { QuickReply } from "@/features/aiscript/aiscript.slice";
import { selectTranscriptText } from "@/features/social-listening/social-listening.slice";
import ReactMarkdown from "react-markdown";

export default function AiScript() {
  const dispatch = useDispatch();
  const messages = useSelector(selectAiscriptMessages);
  const isLoading = useSelector(selectAiscriptLoading);
  const isOpen = useSelector(selectAiscriptOpen);
  const [inputMessage, setInputMessage] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const isAuth = useSelector(selectIsAuthenticated);
  const [expandedMessages, setExpandedMessages] = useState<Set<number>>(
    new Set()
  );
  const [isExpanded, setIsExpanded] = useState(false);
  const [isClosing, setIsClosing] = useState(false);
  const [copiedMessageIndex, setCopiedMessageIndex] = useState<number | null>(
    null
  );
  const transcriptText = useSelector(selectTranscriptText);

  // Add tracking for transcript mode and store the transcript text
  const [isTranscriptMode, setIsTranscriptMode] = useState(false);
  const currentTranscriptRef = useRef<string | null>(null);

  // Track when we should show quick replies for transcript
  const [shouldShowTranscriptOptions, setShouldShowTranscriptOptions] =
    useState(false);

  // Character limit for message truncation
  const CHARACTER_LIMIT = 255;

  // Add state for typing animation
  const [typingIndex, setTypingIndex] = useState<number | null>(null);
  const [typingText, setTypingText] = useState("");
  const [typingComplete, setTypingComplete] = useState(true);
  const typingSpeed = 15; // ms per character
  const maxTypingDuration = 3000; // Maximum typing animation duration (3s)

  // Add ref for textarea
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Scroll to bottom when messages update or during typing
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages, isOpen, typingText]);

  // Monitor for new messages to animate
  useEffect(() => {
    if (!isLoading && messages.length > 0 && typingComplete) {
      const firstMessage = messages[0];

      console.log("firstMessage", firstMessage);

      // Check if this is a transcript message that needs quick replies
      if (
        firstMessage.type === "ai" &&
        firstMessage.content?.includes("Original script:")
      ) {
        setShouldShowTranscriptOptions(true);
        setIsTranscriptMode(true);

        // Store the transcript for future use
        const scriptContent = firstMessage.content
          .split("Original script:")[1]
          ?.trim();
        if (scriptContent) {
          currentTranscriptRef.current = scriptContent;
        }
      }

      // Only animate AI messages
      if (firstMessage.type === "ai") {
        startTypingAnimation(firstMessage.content, messages.length - 1);
      }
    }
  }, [messages, isLoading]);

  // Reset transcript mode when conversation is cleared or session ends
  useEffect(() => {
    if (messages.length === 0) {
      setIsTranscriptMode(false);
      currentTranscriptRef.current = null;
    }
  }, [messages.length]);

  // Listen for multiple conditions to show transcript quick replies
  useEffect(() => {
    if (shouldShowTranscriptOptions && typingComplete && transcriptText) {
      // Extract just the transcript part
      let extractedTranscript = transcriptText;
      // Remove "Original script:" if present
      if (transcriptText.includes("Original script:")) {
        extractedTranscript = extractedTranscript
          .replace("Original script:", "")
          .trim();
      }

      // Mark as processed to prevent showing quick replies multiple times
      setShouldShowTranscriptOptions(false);
      dispatch(socialActions.setTranscript(null));
    }
  }, [shouldShowTranscriptOptions, typingComplete, transcriptText]);

  // Function to animate text typing
  const startTypingAnimation = (text: string, index: number) => {
    // If it's already typing or no text to type, do nothing
    if (!typingComplete || !text) return;

    setTypingIndex(index);
    setTypingText("");
    setTypingComplete(false);

    let charIndex = -1;
    const startTime = Date.now();

    const typeChar = () => {
      // If we've exceeded max duration, show full text immediately
      if (Date.now() - startTime > maxTypingDuration) {
        setTypingText(text);
        setTypingComplete(true);
        setTypingIndex(null);
        return;
      }

      // If we've typed all characters
      if (charIndex >= text.length) {
        setTypingComplete(true);
        setTypingIndex(null);
        return;
      }

      // Add the next character
      setTypingText((prev) => prev + text.charAt(charIndex));
      charIndex++;

      // Schedule the next character
      setTimeout(typeChar, typingSpeed);
    };

    // Start typing
    typeChar();
  };

  // Add function to auto-resize textarea
  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      // Reset height first to get the correct scrollHeight
      textarea.style.height = "auto";
      // Set the height to scrollHeight with a maximum
      const maxHeight = 150; // maximum height in pixels
      textarea.style.height = `${Math.min(textarea.scrollHeight, maxHeight)}px`;
    }
  };

  // Adjust textarea height when input message changes
  useEffect(() => {
    adjustTextareaHeight();
  }, [inputMessage]);

  const handleSendMessage = () => {
    if (!inputMessage.trim() || isLoading) return;

    console.log("currentTranscriptRef.current", currentTranscriptRef.current);
    console.log("isTranscriptMode", isTranscriptMode);
    // Check if we're in transcript mode and this is the first user message
    if (isTranscriptMode && currentTranscriptRef.current) {
      // Append the transcript to the user's message
      const messageWithTranscript = `${inputMessage.trim()}\n\n${
        currentTranscriptRef.current
      }`;
      console.log(
        "AIScript inputMessage with transcript",
        messageWithTranscript
      );
      dispatch(
        actions.sendUserPrompt({
          displayText: inputMessage,
          content: messageWithTranscript,
        })
      );

      // Exit transcript mode after the first message
      setIsTranscriptMode(false);
    } else {
      // Regular message handling
      console.log("AIScript inputMessage", inputMessage);
      dispatch(actions.sendMessage(inputMessage));
    }

    setInputMessage("");
  };

  // Handle key press in textarea
  const handleKeyPress = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // If Enter is pressed without Shift, send message
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault(); // Prevent newline
      handleSendMessage();
    }
  };

  // Handle quick reply clicks
  const handleQuickReply = (reply: QuickReply) => {
    if (isLoading) return;

    // Display only the label in the UI but send the full content and promptId to the backend
    dispatch(
      actions.sendUserPrompt({
        displayText: reply.label,
        content: reply.content,
        promptId: reply.promptId, // Include the promptId if available
      })
    );

    // Exit transcript mode since we've used a quick reply
    setIsTranscriptMode(false);
  };

  const handleCloseScript = () => {
    // If expanded, collapse first then close
    if (isExpanded) {
      setIsClosing(true);
      setIsExpanded(false);

      // Wait for collapse animation to finish before closing
      setTimeout(() => {
        if (isAuth) {
          dispatch(actions.endSession());
        } else {
          dispatch(actions.closeAIScript());
          dispatch(actions.clearMessages());
        }
        setIsClosing(false);
      }, 300); // Match this duration with the CSS transition duration
    } else {
      // If not expanded, close immediately
      if (isAuth) {
        dispatch(actions.endSession());
      } else {
        dispatch(actions.closeAIScript());
        dispatch(actions.clearMessages());
      }
    }
  };

  // Toggle script when button is clicked
  const toggleScript = () => {
    dispatch(actions.toggleScript());
  };

  // Check if a message is expanded
  const isMessageExpanded = (index: number) => expandedMessages.has(index);

  // Toggle message expansion
  const toggleMessageExpansion = (index: number) => {
    const newExpandedMessages = new Set(expandedMessages);
    if (newExpandedMessages.has(index)) {
      newExpandedMessages.delete(index);
    } else {
      newExpandedMessages.add(index);
    }
    setExpandedMessages(newExpandedMessages);

    // Give a small delay before scrolling to ensure the DOM has updated
    setTimeout(() => {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }, 50);
  };

  // Format message content with consideration for typing animation
  const formatMessageContent = (content: string, index: number) => {
    const isLongMessage = content && content.length > CHARACTER_LIMIT;

    // Create a formatted content element with markdown support
    const createContentElement = (text: string) => {
      if (!text) return <span></span>;

      // Handle special case for "Original script:" heading
      let processedText = text;
      const hasOriginalScript = text.includes("Original script:");

      if (hasOriginalScript) {
        const parts = text.split("Original script:");
        return (
          <>
            <ReactMarkdown>{parts[0]}</ReactMarkdown>
            {parts[1] && (
              <>
                <div className="font-bold w-full">Original script:</div>
                <ReactMarkdown>{parts[1]}</ReactMarkdown>
              </>
            )}
          </>
        );
      }

      return <ReactMarkdown>{processedText}</ReactMarkdown>;
    };

    if (!content) {
      return createContentElement("");
    }

    if (!isLongMessage) {
      return createContentElement(content);
    }

    if (isMessageExpanded(index)) {
      return (
        <>
          {createContentElement(content)}{" "}
          <button
            onClick={(e) => {
              e.stopPropagation();
              toggleMessageExpansion(index);
            }}
            className="text-xs text-blue-500 hover:underline inline-block ml-1 cursor-pointer"
          >
            Show less
          </button>
        </>
      );
    }

    return (
      <>
        {createContentElement(content.substring(0, CHARACTER_LIMIT))}
        <span>...</span>{" "}
        <button
          onClick={(e) => {
            e.stopPropagation();
            toggleMessageExpansion(index);
          }}
          className="text-xs text-blue-500 hover:underline inline-block ml-1 cursor-pointer"
        >
          Read more
        </button>
      </>
    );
  };

  // Handle expand/collapse for the script box
  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
    // Give a small delay before scrolling to ensure the DOM has updated after expansion
    setTimeout(() => {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }, 50);
  };

  // Function to copy message content to clipboard
  const handleCopyMessage = (content: string, index: number) => {
    navigator.clipboard.writeText(content).then(
      () => {
        // Set the index of the copied message to show feedback
        setCopiedMessageIndex(index);

        // Reset the copied state after 2 seconds
        setTimeout(() => {
          setCopiedMessageIndex(null);
        }, 2000);
      },
      (err) => {
        console.error("Could not copy text: ", err);
      }
    );
  };

  return (
    <div
      className={`fixed ${
        isExpanded ? "right-0 bottom-0 z-100" : "bottom-2 right-2"
      } z-40 bg-transparent font-['Inter',_'system-ui',_sans-serif]`}
    >
      <button
        onClick={toggleScript}
        className="h-12 rounded-full bg-gray-900 hover:bg-black 
  dark:bg-gray-800 dark:hover:bg-gray-700 text-white flex items-center 
  justify-center shadow-lg transition-all duration-200 px-3 gap-2"
      >
        <Sparkles size={20} />
        <span className="text-white text-sm font-medium">Affitor AI</span>
      </button>

      {isOpen && (
        <div
          className={`absolute md:min-h-[500px] min-h-[70vh] ${
            isExpanded
              ? "bottom-0 right-0 w-[100vw] h-[100vh] lg:p-[20px] max-w-none"
              : "bottom-10 right-0 w-96 max-w-[95vw] max-h-[60vh]"
          } shadow-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 transition-all duration-300 rounded-2xl flex flex-col overflow-hidden ${
            isClosing ? "opacity-90 scale-95" : ""
          }`}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100">
            <div>
              <h3 className="font-semibold text-base">Affitor AI</h3>
            </div>
            <div className="flex gap-2">
              <button
                onClick={toggleExpand}
                className="w-8 h-8 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg flex items-center justify-center transition-colors text-gray-500 dark:text-gray-400"
                title={isExpanded ? "Collapse" : "Expand"}
              >
                <i
                  className={`fas ${isExpanded ? "fa-compress" : "fa-expand"}`}
                ></i>
              </button>
              <button
                onClick={handleCloseScript}
                className="w-8 h-8 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg flex items-center justify-center transition-colors text-gray-500 dark:text-gray-400"
              >
                ×
              </button>
            </div>
          </div>

          {/* Messages */}
          <div
            className={`flex-1 overflow-y-auto p-6 bg-white dark:bg-gray-900 ${
              messages.length === 0
                ? "flex items-center justify-center"
                : "space-y-4"
            }`}
          >
            {/* Welcome message when no messages */}
            {messages.length === 0 && isAuth && (
              <div className="text-center">
                <h2 className="font-bold text-lg text-gray-900 dark:text-gray-100">
                  How can I help you today?
                </h2>
              </div>
            )}

            {messages.map((message: any, index: number) => (
              <div
                key={index}
                className={`flex flex-col ${
                  message.type === "user" ? "items-end" : "items-start"
                }`}
              >
                {message.type === "user" ? (
                  // User message - right-aligned with margin
                  <div className="flex justify-end w-full">
                    <div
                      className={`max-w-[80%] bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-2xl px-4 py-3 mr-1`}
                    >
                      <div
                        className="text-sm leading-relaxed font-normal break-words whitespace-pre-wrap font-sans"
                        style={{
                          fontFamily:
                            '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
                        }}
                      >
                        {formatMessageContent(message.content, index)}
                      </div>
                    </div>
                  </div>
                ) : (
                  // AI message - plain text like Claude (no background)
                  <div className="flex justify-start w-full">
                    <div className="w-full mx-1">
                      <div
                        className={`text-sm leading-relaxed font-normal text-gray-900 dark:text-gray-100 break-words whitespace-pre-wrap font-sans ${
                          index === 0 ? "font-bold text-base" : ""
                        }`}
                        style={{
                          fontFamily:
                            '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
                        }}
                      >
                        {index === typingIndex ? (
                          <>
                            {typingText.includes("Original script:") ? (
                              <>
                                {(() => {
                                  const parts =
                                    typingText.split("Original script:");
                                  return (
                                    <>
                                      <ReactMarkdown
                                        components={{
                                          p: ({ children }) => (
                                            <p className="mb-1">{children}</p>
                                          ),
                                          h1: ({ children }) => (
                                            <h1 className="text-lg font-bold mb-2">
                                              {children}
                                            </h1>
                                          ),
                                          h2: ({ children }) => (
                                            <h2 className="text-base font-bold mb-1.5">
                                              {children}
                                            </h2>
                                          ),
                                        }}
                                      >
                                        {parts[0]}
                                      </ReactMarkdown>
                                      {parts[1] && (
                                        <>
                                          <div className="font-bold w-full mb-1">
                                            Original script:
                                          </div>
                                          <ReactMarkdown
                                            components={{
                                              p: ({ children }) => (
                                                <p className="mb-1">
                                                  {children}
                                                </p>
                                              ),
                                            }}
                                          >
                                            {parts[1]}
                                          </ReactMarkdown>
                                        </>
                                      )}
                                    </>
                                  );
                                })()}
                              </>
                            ) : (
                              <ReactMarkdown
                                components={{
                                  p: ({ children }) => (
                                    <p className="mb-1">{children}</p>
                                  ),
                                  h1: ({ children }) => (
                                    <h1 className="text-lg font-bold mb-2">
                                      {children}
                                    </h1>
                                  ),
                                  h2: ({ children }) => (
                                    <h2 className="text-base font-bold mb-1.5">
                                      {children}
                                    </h2>
                                  ),
                                }}
                              >
                                {typingText}
                              </ReactMarkdown>
                            )}
                            <span className="inline-block w-2 h-4 bg-gray-400 dark:bg-gray-500 ml-1 animate-pulse"></span>
                          </>
                        ) : (
                          formatMessageContent(message.content, index)
                        )}
                      </div>

                      {/* Copy button for every AI message */}
                      {message.type === "ai" &&
                        (index !== typingIndex || typingComplete) && (
                          <button
                            onClick={() =>
                              handleCopyMessage(message.content, index)
                            }
                            className="mt-3 px-0 py-0 hover:bg-gray-100 dark:hover:bg-gray-800 hover:px-2 hover:py-1 rounded-md transition-all duration-200 flex items-center gap-1.5 text-xs text-gray-500 dark:text-gray-400 font-sans"
                            style={{
                              fontFamily:
                                '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
                            }}
                            title="Copy response"
                          >
                            {copiedMessageIndex === index ? (
                              <>
                                <span className="text-green-600 dark:text-green-400">
                                  ✓
                                </span>
                                <span className="text-green-600 dark:text-green-400">
                                  Copied
                                </span>
                              </>
                            ) : (
                              <>
                                <Copy size={12} />
                                <span>Copy</span>
                              </>
                            )}
                          </button>
                        )}
                    </div>
                  </div>
                )}

                {/* Remove message-specific quick replies - now shown globally above input */}
              </div>
            ))}

            {/* Loading indicator */}
            {isLoading && (
              <div className="flex justify-start w-full">
                <div className="w-full mx-1">
                  <div
                    className="text-gray-900 dark:text-gray-100 font-sans"
                    style={{
                      fontFamily:
                        '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
                    }}
                  >
                    <div className="flex items-center gap-2">
                      <div className="flex gap-1">
                        <div
                          className="w-1.5 h-1.5 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce"
                          style={{ animationDelay: "0ms" }}
                        ></div>
                        <div
                          className="w-1.5 h-1.5 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce"
                          style={{ animationDelay: "200ms" }}
                        ></div>
                        <div
                          className="w-1.5 h-1.5 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce"
                          style={{ animationDelay: "400ms" }}
                        ></div>
                      </div>
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        Thinking
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />

            {!isAuth && (
              <div className="flex justify-center mt-4 flex-col gap-5 p-6">
                <div className="font-medium text-center flex items-center justify-center gap-2">
                  <span className="text-gray-600 dark:text-gray-300">
                    Authentication Required
                  </span>
                </div>
                <a
                  href="/authentication"
                  className="inline-flex items-center justify-center rounded-md bg-gray-900 dark:bg-gray-800 px-4 py-2 text-xs font-medium text-white hover:bg-black dark:hover:bg-gray-700 transition-colors"
                >
                  Login to continue
                </a>
              </div>
            )}
          </div>

          {/* Input Area */}
          <div className="bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 mt-auto">
            {/* Global quick replies positioned right above input */}
            {messages.length > 0 &&
              messages[messages.length - 1]?.type === "ai" &&
              messages[messages.length - 1]?.quickReplies &&
              (messages[messages.length - 1]?.quickReplies?.length ?? 0) > 0 &&
              (typingIndex === null || typingComplete) && (
                <div className="px-4 pt-2 pb-1">
                  <div className="overflow-x-auto">
                    <div
                      className="flex gap-3 pb-2"
                      style={{ width: "max-content" }}
                    >
                      {messages[messages.length - 1]?.quickReplies?.map(
                        (reply: QuickReply, replyIdx: number) => (
                          <button
                            key={replyIdx}
                            onClick={() => handleQuickReply(reply)}
                            className="px-3 py-1.5 rounded-full bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs border border-gray-200 dark:border-gray-600 shadow-sm transition-all duration-200 whitespace-nowrap flex-shrink-0 font-medium font-sans"
                            style={{
                              fontFamily:
                                '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
                            }}
                          >
                            {reply.label}
                          </button>
                        )
                      )}
                    </div>
                  </div>
                </div>
              )}

            <div className="p-2">
              <div className="relative">
                <textarea
                  ref={textareaRef}
                  placeholder={
                    isAuth
                      ? isLoading
                        ? "AI is thinking..."
                        : "Ask AI anything..."
                      : "Sign in to use AI"
                  }
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  onKeyDown={handleKeyPress}
                  disabled={!isAuth || isLoading}
                  className={`w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-xl resize-none focus:outline-none focus:ring-2 focus:ring-gray-400 dark:focus:ring-gray-500 focus:border-transparent disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500 dark:disabled:text-gray-400 text-sm leading-relaxed font-normal transition-all duration-200 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400`}
                  style={{ minHeight: "56px" }}
                  autoFocus={isAuth}
                  rows={1}
                />

                {/* Send button inside the textarea */}
                <button
                  onClick={handleSendMessage}
                  disabled={!isAuth || isLoading || !inputMessage.trim()}
                  className="absolute right-2 bottom-3 w-6 h-6 bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600 disabled:bg-gray-300 dark:disabled:bg-gray-600 text-white rounded-full flex items-center justify-center transition-all duration-200 disabled:cursor-not-allowed"
                >
                  {isLoading ? (
                    <div className="w-2 h-2 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  ) : (
                    <Send size={10} />
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
